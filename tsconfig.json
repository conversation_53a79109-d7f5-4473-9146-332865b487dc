{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "outDir": "dist", "baseUrl": ".", "declaration": true, "sourceMap": true, "rootDir": ".", "paths": {"@/*": ["src/*"], "@utils/*": ["src/utils/*"], "@config/*": ["src/config/*"], "@types/*": ["src/types/*"]}, "types": ["jest", "node", "supertest"]}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "coverage", "reports"], "ts-jest": {"tsconfig": "tsconfig.json"}}