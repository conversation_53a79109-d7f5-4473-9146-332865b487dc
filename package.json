{"name": "qa-automation-digisac-jest", "version": "1.0.0", "description": "Projeto de testes automatizados de API para a plataforma Digisac usando Jest + Supertest", "main": "index.js", "type": "module", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:smoke": "jest --testNamePattern='smoke'", "test:api": "jest --testPathPattern='tests/api'", "test:performance": "jest --testNamePattern='performance'", "test:debug": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "lint": "eslint src tests --ext .ts,.js", "lint:fix": "eslint src tests --ext .ts,.js --fix", "build": "tsc", "clean": "rimraf dist coverage", "setup": "npm install && npm run build", "report:coverage": "jest --coverage --coverageReporters=html", "report:junit": "jest --reporters=default --reporters=jest-junit"}, "keywords": ["jest", "supertest", "api-testing", "digisac", "typescript", "automation", "qa"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-plugin-jest": "^27.6.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-junit": "^16.0.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "dependencies": {"@faker-js/faker": "^8.2.0", "ajv": "^8.12.0", "ajv-formats": "^3.0.1", "dotenv": "^16.3.1", "joi": "^17.11.0", "supertest": "^6.3.3"}}