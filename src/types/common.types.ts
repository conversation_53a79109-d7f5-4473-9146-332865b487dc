/**
 * Tipos comuns utilizados em todo o projeto de testes da API Digisac
 */

// Tipos de resposta da API
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success?: boolean;
  total?: number;
  page?: number;
  perPage?: number;
}

// Tipos de erro da API
export interface ApiError {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
  path?: string;
}

// Tipos de paginação
export interface PaginationParams {
  page?: number;
  perPage?: number;
  order?: string;
  orderBy?: string;
}

// Tipos de filtros
export interface FilterParams {
  where?: Record<string, any>;
  include?: string[];
  query?: Record<string, any>;
}

// Tipos de autenticação OAuth 2.0
export interface OAuthTokenRequest {
  grant_type: 'password';
  client_id: string;
  client_secret: string;
  username: string;
  password: string;
}

export interface OAuthTokenResponse {
  access_token: string;
  token_type: 'Bearer';
  expires_in: number;
  refresh_token: string;
}

// Tipos de credenciais
export interface UserCredentials {
  username: string;
  password: string;
}

export interface OAuthClient {
  client_id: string;
  client_secret: string;
}

// Tipos de validação
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// Tipos de performance
export interface PerformanceMetrics {
  responseTime: number;
  throughput: number;
  memoryUsage: number;
  cpuUsage: number;
}

export interface LoadTestResult {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
  throughput: number;
}

// Tipos de teste
export interface TestConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  environment: string;
}

export interface TestData {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// Tipos de status
export type Status = 'active' | 'inactive' | 'pending' | 'completed' | 'failed';

export type ServiceType = 'whatsapp' | 'telegram' | 'email' | 'sms';

export type MessageType = 'text' | 'image' | 'audio' | 'video' | 'document' | 'location' | 'contact' | 'sticker';

export type TicketStatus = 'open' | 'closed' | 'pending' | 'in_progress' | 'waiting' | 'resolved';

export type UserRole = 'admin' | 'operator' | 'supervisor' | 'manager';

// Tipos de arquivo
export interface FileData {
  base64: string;
  mimetype: string;
  name: string;
  size?: number;
}

// Tipos de notificação
export interface NotificationData {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  read: boolean;
  createdAt: string;
}

// Tipos de métricas
export interface MetricData {
  id: string;
  name: string;
  value: number;
  unit: string;
  timestamp: string;
  tags?: Record<string, string>;
}

// Tipos de configuração
export interface ConfigData {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
  required: boolean;
}

// Tipos de cache
export interface CacheData<T = any> {
  key: string;
  value: T;
  expiresAt: number;
  createdAt: number;
}

// Tipos de log
export interface LogData {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  timestamp: string;
  context?: Record<string, any>;
  stack?: string;
}
