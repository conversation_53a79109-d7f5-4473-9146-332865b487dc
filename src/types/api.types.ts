/**
 * Tipos específicos da API Digisac baseados na documentação oficial
 * Fonte: digisac-api-doc.md
 */

import { Status, ServiceType, MessageType, TicketStatus, UserRole } from './common.types';

// ===== AGENDAMENTOS (Schedule) =====
export interface Schedule {
  id: string;
  contactId: string;
  departmentId: string;
  files: any[];
  message: string;
  notes?: string;
  notificateUser: boolean;
  openTicket: boolean;
  scheduledAt: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateScheduleRequest {
  contactId: string;
  departmentId: string;
  files?: any[];
  message: string;
  notes?: string;
  notificateUser: boolean;
  openTicket: boolean;
  scheduledAt: string;
  userId: string;
}

// ===== TEMPO REAL (Now) =====
export interface DepartmentsResume {
  id: string;
  name: string;
  activeUsers: number;
  totalUsers: number;
  openTickets: number;
  closedTickets: number;
}

export interface AttendanceResume {
  id: string;
  name: string;
  email: string;
  departmentId: string;
  departmentName: string;
  status: 'online' | 'offline' | 'busy';
  openTickets: number;
  closedTickets: number;
}

export interface NowResume {
  departments: DepartmentsResume[];
  attendance: AttendanceResume[];
  totalOpenTickets: number;
  totalClosedTickets: number;
}

// ===== ASSUNTOS DE CHAMADO (Ticket Topics) =====
export interface TicketTopic {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTicketTopicRequest {
  name: string;
}

// ===== AUDITORIA (Auth History) =====
export interface AuthHistory {
  id: string;
  userId: string;
  userName: string;
  action: string;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
}

// ===== AVALIAÇÕES (Questions) =====
export interface Question {
  id: string;
  name: string;
  duration: number;
  type: 'NPS' | 'CSAT';
  questionMessage: string;
  tries: number;
  successMessage: string;
  invalidMessage: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateQuestionRequest {
  name: string;
  duration: number;
  type: 'NPS' | 'CSAT';
  questionMessage: string;
  tries: number;
  successMessage: string;
  invalidMessage: string;
}

// ===== CAMPANHAS (Campaigns) =====
export interface Campaign {
  id: string;
  name: string;
  message: string;
  status: 'draft' | 'scheduled' | 'running' | 'completed' | 'cancelled';
  scheduledAt?: string;
  totalContacts: number;
  sentContacts: number;
  deliveredContacts: number;
  readContacts: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCampaignRequest {
  name: string;
  message: string;
  scheduledAt?: string;
  contactIds: string[];
  serviceId: string;
}

export interface CampaignStats {
  campaignId: string;
  totalContacts: number;
  sentContacts: number;
  deliveredContacts: number;
  readContacts: number;
  errorContacts: number;
  successRate: number;
}

// ===== CAMPOS PERSONALIZADOS (Custom Fields) =====
export interface CustomField {
  id: string;
  name: string;
  type: 'text' | 'number' | 'email' | 'phone' | 'date' | 'select' | 'multiselect';
  allowed: 'contacts' | 'tickets' | 'users';
  options?: string[];
  required: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomFieldRequest {
  name: string;
  type: 'text' | 'number' | 'email' | 'phone' | 'date' | 'select' | 'multiselect';
  allowed: 'contacts' | 'tickets' | 'users';
  options?: string[];
  required?: boolean;
}

// ===== CARGOS (Roles) =====
export interface Role {
  id: string;
  displayName: string;
  isAdmin: boolean;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateRoleRequest {
  displayName: string;
  isAdmin: boolean;
  permissions: string[];
}

// ===== CONEXÕES/SERVICES (Services) =====
export interface Service {
  id: string;
  name: string;
  type: ServiceType;
  token: string;
  archivedAt?: string;
  data: {
    status: {
      isStarted: boolean;
      isStarting: boolean;
      isConnected: boolean;
    };
    webchat: any;
    syncFlowDone: boolean;
    lastShutdownAt?: string;
    isManuallyDisconnected: boolean;
  };
  settings: {
    readReceipts: boolean;
    reactionsEnabled: boolean;
    blockMessageRulesActive: boolean;
    markComposingBeforeSend: boolean;
    unblockByReceiveMessage: boolean;
    shouldOpenTicketForGroups: boolean;
  };
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  accountId: string;
  botId?: string;
  defaultDepartmentId: string;
  health: any;
  isMultiDevice: boolean;
  webchatIdleStage: any;
}

export interface CreateServiceRequest {
  name: string;
  type: ServiceType;
  serviceToken: string;
  webHook?: string;
  max_count: number;
}

// ===== CONTATOS (Contacts) =====
export interface Contact {
  id: string;
  internalName: string;
  number: string;
  serviceId: string;
  defaultDepartmentId?: string;
  tagIds: string[];
  customFields: CustomFieldValue[];
  personId?: string;
  organizationId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CustomFieldValue {
  id: string;
  value: string;
}

export interface CreateContactRequest {
  internalName: string;
  number: string;
  serviceId: string;
  defaultDepartmentId?: string;
  tagIds?: string[];
  customFields?: CustomFieldValue[];
}

export interface CreateManyContactsRequest {
  contacts: CreateContactRequest[];
}

// ===== DEPARTAMENTOS (Departments) =====
export interface Department {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  archivedAt?: string;
  accountId: string;
  distributionId?: string;
  usersCount?: string;
}

export interface CreateDepartmentRequest {
  name: string;
}

// ===== ENTIDADES (Entities) =====
export interface Entity {
  id: string;
  name: string;
  description: string;
  values: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateEntityRequest {
  name: string;
  description: string;
  values: string[];
}

// ===== FERIADOS (Holidays) =====
export interface Holiday {
  id: string;
  name: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateHolidayRequest {
  name: string;
  date: string;
}

// ===== FILAS (Queue) =====
export interface Queue {
  id: string;
  departmentId: string;
  departmentName: string;
  waitingContacts: number;
  averageWaitTime: number;
  createdAt: string;
}

// ===== MENSAGENS (Messages) =====
export interface Message {
  id: string;
  text?: string;
  type: MessageType;
  number: string;
  serviceId: string;
  ticketId?: string;
  contactId?: string;
  userId?: string;
  origin: 'bot' | 'user';
  file?: FileData;
  createdAt: string;
  updatedAt: string;
}

export interface FileData {
  base64: string;
  mimetype: string;
  name: string;
}

export interface CreateMessageRequest {
  text?: string;
  number: string;
  serviceId: string;
  ticketId?: string;
  contactId?: string;
  userId?: string;
  origin?: 'bot' | 'user';
  file?: FileData;
}

// ===== PESSOAS (Persons) =====
export interface Person {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  jobTitle?: string;
  organizationId?: string;
  contacts?: Contact[];
  createdAt: string;
  updatedAt: string;
}

export interface CreatePersonRequest {
  name: string;
  email?: string;
  phone?: string;
  jobTitle?: string;
  organizationId?: string;
}

// ===== RESPOSTAS RÁPIDAS (Quick Replies) =====
export interface QuickReply {
  id: string;
  text: string;
  departmentIds: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateQuickReplyRequest {
  text: string;
  departmentIds: string[];
}

// ===== ROBÔS (Bots) =====
export interface Bot {
  id: string;
  name: string;
  type: ServiceType;
  description?: string;
  mainDepartmentId: string;
  departmentsIds: string[];
  tagsIds: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateBotRequest {
  name: string;
  type: ServiceType;
  description?: string;
  mainDepartmentId: string;
  departmentsIds: string[];
  tagsIds: string[];
}

// ===== TAGS =====
export interface Tag {
  id: string;
  name: string;
  color: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTagRequest {
  name: string;
  color: string;
}

// ===== TICKETS =====
export interface Ticket {
  id: string;
  name: string;
  contactId: string;
  serviceId: string;
  departmentId: string;
  botId?: string;
  subjectId?: string;
  isRead: boolean;
  inTrash: boolean;
  status: TicketStatus;
  tagsIds: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateTicketRequest {
  name: string;
  contactId: string;
  serviceId: string;
  departmentId: string;
  botId?: string;
  isRead?: boolean;
  inTrash?: boolean;
  tagsIds?: string[];
}

export interface TransferTicketRequest {
  departmentId: string;
  userId: string;
}

// ===== TOKENS =====
export interface Token {
  id: string;
  name: string;
  token: string;
  lastUsedAt?: string;
  createdAt: string;
  expiresAt?: string;
}

// ===== USUÁRIOS (Users) =====
export interface User {
  id: string;
  accountId: string;
  email: string;
  name: string;
  isAdmin: boolean;
  roles?: Role[];
  organizationIds: string[];
  departmentsId: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateUserRequest {
  accountId: string;
  email: string;
  password: string;
  name: string;
  rolesId?: string;
  organizationIds?: string[];
  departmentsId: string[];
}

// ===== HORÁRIOS DE ATENDIMENTO (Schedules) =====
export interface Schedule {
  id: string;
  name: string;
  status: 'active' | 'inactive';
  days: string[];
  startTime: string;
  endTime: string;
  departmentIds: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateScheduleRequest {
  name: string;
  status: 'active' | 'inactive';
  days: string[];
  startTime: string;
  endTime: string;
  departmentIds: string[];
}

// ===== MÉTRICAS (Metrics) =====
export interface Metric {
  id: string;
  name: string;
  value: number;
  unit: string;
  timestamp: string;
  tags?: Record<string, string>;
}

// ===== HISTÓRICO DE CONVERSAS (Chat History) =====
export interface ChatHistoryExport {
  type: 'comma' | 'semiColon';
  ticketId?: string;
  contactId?: string;
  start_date?: string;
  end_date?: string;
}

// ===== TIPOS DE RESPOSTA PAGINADA =====
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  perPage: number;
  totalPages: number;
}

// ===== TIPOS DE FILTROS =====
export interface FilterOptions {
  where?: Record<string, any>;
  include?: string[];
  query?: Record<string, any>;
  order?: string;
  orderBy?: string;
  page?: number;
  perPage?: number;
}

// ===== TIPOS DE EXPORTAÇÃO =====
export interface ExportOptions {
  type: 'comma' | 'semiColon';
  format: 'csv' | 'xlsx' | 'json';
  fields?: string[];
  filters?: FilterOptions;
}
