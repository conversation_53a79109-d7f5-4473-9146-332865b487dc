/**
 * Tipos específicos para testes da API Digisac
 */

import { ApiResponse, ApiError, PerformanceMetrics } from './common.types';

// ===== TIPOS DE TESTE =====
export interface TestCase {
  id: string;
  name: string;
  description: string;
  category: TestCategory;
  priority: TestPriority;
  status: TestStatus;
  steps: TestStep[];
  expectedResult: string;
  actualResult?: string;
  duration?: number;
  error?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TestStep {
  id: string;
  description: string;
  action: string;
  expected: string;
  actual?: string;
  status: 'passed' | 'failed' | 'skipped' | 'pending';
  duration?: number;
  error?: string;
}

export type TestCategory = 
  | 'smoke'
  | 'functional'
  | 'integration'
  | 'performance'
  | 'security'
  | 'api'
  | 'ui'
  | 'e2e';

export type TestPriority = 'low' | 'medium' | 'high' | 'critical';

export type TestStatus = 
  | 'pending'
  | 'running'
  | 'passed'
  | 'failed'
  | 'skipped'
  | 'blocked'
  | 'retry';

// ===== TIPOS DE SUÍTE DE TESTES =====
export interface TestSuite {
  id: string;
  name: string;
  description: string;
  category: TestCategory;
  testCases: TestCase[];
  status: TestStatus;
  duration?: number;
  passed: number;
  failed: number;
  skipped: number;
  total: number;
  createdAt: string;
  updatedAt: string;
}

// ===== TIPOS DE RELATÓRIO =====
export interface TestReport {
  id: string;
  name: string;
  description: string;
  testSuite: TestSuite;
  environment: string;
  browser?: string;
  version?: string;
  status: TestStatus;
  duration: number;
  passed: number;
  failed: number;
  skipped: number;
  total: number;
  coverage?: number;
  performance?: PerformanceMetrics;
  createdAt: string;
  updatedAt: string;
}

// ===== TIPOS DE DADOS DE TESTE =====
export interface TestData {
  id: string;
  name: string;
  description: string;
  category: string;
  data: Record<string, any>;
  isValid: boolean;
  isUsed: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TestFixture {
  id: string;
  name: string;
  description: string;
  category: string;
  data: Record<string, any>;
  setup?: string[];
  teardown?: string[];
  dependencies?: string[];
  createdAt: string;
  updatedAt: string;
}

// ===== TIPOS DE CONFIGURAÇÃO DE TESTE =====
export interface TestConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  environment: string;
  parallel: boolean;
  workers: number;
  headless: boolean;
  debug: boolean;
  verbose: boolean;
  coverage: boolean;
  reports: string[];
}

export interface TestEnvironment {
  name: string;
  baseUrl: string;
  apiKey?: string;
  username?: string;
  password?: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  headers: Record<string, string>;
  cookies: Record<string, string>;
}

// ===== TIPOS DE ASSERTION =====
export interface Assertion {
  id: string;
  name: string;
  description: string;
  type: AssertionType;
  expected: any;
  actual: any;
  operator: AssertionOperator;
  status: 'passed' | 'failed' | 'skipped';
  message?: string;
  duration?: number;
  error?: string;
}

export type AssertionType = 
  | 'equals'
  | 'notEquals'
  | 'contains'
  | 'notContains'
  | 'greaterThan'
  | 'lessThan'
  | 'greaterThanOrEqual'
  | 'lessThanOrEqual'
  | 'isTrue'
  | 'isFalse'
  | 'isNull'
  | 'isNotNull'
  | 'isUndefined'
  | 'isDefined'
  | 'isArray'
  | 'isObject'
  | 'isString'
  | 'isNumber'
  | 'isBoolean'
  | 'isDate'
  | 'matches'
  | 'notMatches'
  | 'hasLength'
  | 'hasProperty'
  | 'hasMethod'
  | 'throws'
  | 'notThrows';

export type AssertionOperator = 
  | '==='
  | '!=='
  | '=='
  | '!='
  | '>'
  | '<'
  | '>='
  | '<='
  | 'includes'
  | 'notIncludes'
  | 'startsWith'
  | 'endsWith'
  | 'matches'
  | 'notMatches';

// ===== TIPOS DE MOCK =====
export interface MockData {
  id: string;
  name: string;
  description: string;
  method: string;
  url: string;
  status: number;
  response: any;
  headers?: Record<string, string>;
  delay?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface MockResponse {
  status: number;
  headers: Record<string, string>;
  body: any;
  delay?: number;
}

// ===== TIPOS DE COBERTURA =====
export interface CoverageReport {
  id: string;
  name: string;
  description: string;
  totalLines: number;
  coveredLines: number;
  coverage: number;
  files: CoverageFile[];
  createdAt: string;
  updatedAt: string;
}

export interface CoverageFile {
  name: string;
  path: string;
  totalLines: number;
  coveredLines: number;
  coverage: number;
  lines: CoverageLine[];
}

export interface CoverageLine {
  number: number;
  covered: boolean;
  hits?: number;
  branches?: CoverageBranch[];
}

export interface CoverageBranch {
  line: number;
  type: string;
  covered: boolean;
  hits?: number;
}

// ===== TIPOS DE PERFORMANCE =====
export interface PerformanceTest {
  id: string;
  name: string;
  description: string;
  duration: number;
  requests: number;
  concurrency: number;
  rampUp: number;
  rampDown: number;
  thresholds: PerformanceThreshold[];
  results?: PerformanceResult;
  createdAt: string;
  updatedAt: string;
}

export interface PerformanceThreshold {
  name: string;
  value: number;
  operator: '>' | '<' | '>=' | '<=' | '==';
  unit: 'ms' | 's' | 'rps' | '%';
}

export interface PerformanceResult {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
  throughput: number;
  errorRate: number;
  percentiles: Record<string, number>;
  thresholds: PerformanceThresholdResult[];
}

export interface PerformanceThresholdResult {
  name: string;
  threshold: PerformanceThreshold;
  actual: number;
  passed: boolean;
}

// ===== TIPOS DE LOG =====
export interface TestLog {
  id: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  context: Record<string, any>;
  timestamp: string;
  testCaseId?: string;
  testSuiteId?: string;
  duration?: number;
  stack?: string;
}

// ===== TIPOS DE ERRO =====
export interface TestError {
  id: string;
  name: string;
  message: string;
  stack: string;
  type: string;
  testCaseId?: string;
  testSuiteId?: string;
  timestamp: string;
  context: Record<string, any>;
}

// ===== TIPOS DE RETRY =====
export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  retryCondition: (error: any) => boolean;
  backoffMultiplier: number;
  maxRetryDelay: number;
}

export interface RetryResult {
  attempts: number;
  success: boolean;
  error?: any;
  duration: number;
}

// ===== TIPOS DE PARALELIZAÇÃO =====
export interface ParallelConfig {
  enabled: boolean;
  workers: number;
  maxConcurrency: number;
  timeout: number;
  retries: number;
}

export interface ParallelResult {
  total: number;
  successful: number;
  failed: number;
  duration: number;
  errors: TestError[];
}

// ===== TIPOS DE HOOKS =====
export interface TestHook {
  id: string;
  name: string;
  type: 'beforeAll' | 'afterAll' | 'beforeEach' | 'afterEach';
  function: string;
  timeout: number;
  retries: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface HookResult {
  hookId: string;
  name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
  timestamp: string;
}
