/**
 * Helper de autenticação para API Digisac
 * Migrado do Playwright para Jest + Supertest
 */

import { ApiClient } from './apiClient';
import { OAuthTokenRequest, OAuthTokenResponse, UserCredentials, OAuthClient } from '../types/common.types';
import { API_ENDPOINTS } from '../config/apiEndpoints';

export class AuthHelper {
  private apiClient: ApiClient;
  private tokenCache: Map<string, OAuthTokenResponse> = new Map();
  private tokenExpiry: Map<string, number> = new Map();

  constructor(baseUrl: string) {
    this.apiClient = new ApiClient(baseUrl);
  }

  /**
   * Obtém credenciais das variáveis de ambiente
   */
  getCredentialsFromEnv(): UserCredentials {
    const username = process.env.API_USERNAME;
    const password = process.env.API_PASSWORD;

    if (!username || !password) {
      throw new Error(
        'Credenciais não encontradas nas variáveis de ambiente. ' +
        'Configure API_USERNAME e API_PASSWORD no arquivo .env'
      );
    }

    return { username, password };
  }

  /**
   * Obtém configuração OAuth das variáveis de ambiente
   */
  getOAuthClientFromEnv(): OAuthClient {
    const client_id = process.env.API_CLIENT_ID || 'api';
    const client_secret = process.env.API_CLIENT_SECRET || 'secret';

    return { client_id, client_secret };
  }

  /**
   * Gera token OAuth 2.0 usando Password Grant
   */
  async getOAuthToken(
    credentials: UserCredentials,
    client: OAuthClient
  ): Promise<OAuthTokenResponse> {
    const cacheKey = `${credentials.username}_${client.client_id}`;
    
    // Verificar cache
    const cachedToken = this.tokenCache.get(cacheKey);
    const tokenExpiry = this.tokenExpiry.get(cacheKey);
    
    if (cachedToken && tokenExpiry && Date.now() < tokenExpiry) {
      console.log('Token OAuth obtido do cache');
      return cachedToken;
    }

    console.log('Solicitando novo token OAuth...');

    const tokenRequest: OAuthTokenRequest = {
      grant_type: 'password',
      client_id: client.client_id,
      client_secret: client.client_secret,
      username: credentials.username,
      password: credentials.password
    };

    try {
      const response = await this.apiClient.post(API_ENDPOINTS.AUTH.TOKEN, tokenRequest);
      
      if (response.status !== 200) {
        throw new Error(
          `Falha na autenticação: ${response.status} - ${JSON.stringify(response.body)}`
        );
      }

      const tokenResponse: OAuthTokenResponse = response.body;
      
      // Validar formato do token (deve ser hash de 40 caracteres hexadecimais)
      this.validateTokenFormat(tokenResponse.access_token);
      
      // Cache do token
      this.tokenCache.set(cacheKey, tokenResponse);
      this.tokenExpiry.set(cacheKey, Date.now() + (tokenResponse.expires_in * 1000));
      
      console.log('Token OAuth obtido com sucesso');
      return tokenResponse;
      
    } catch (error) {
      console.error('Erro ao obter token OAuth:', error);
      throw new Error(`Falha na autenticação: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Valida formato do token de acesso
   */
  private validateTokenFormat(token: string): void {
    // Token deve ser hash de 40 caracteres hexadecimais
    const hexPattern = /^[a-fA-F0-9]{40}$/;
    
    if (!hexPattern.test(token)) {
      throw new Error(
        `Formato de token inválido. Esperado: hash de 40 caracteres hexadecimais, ` +
        `recebido: ${token} (${token.length} caracteres)`
      );
    }
  }

  /**
   * Obtém token de acesso autenticado
   */
  async getAuthenticatedToken(): Promise<string> {
    const credentials = this.getCredentialsFromEnv();
    const client = this.getOAuthClientFromEnv();
    
    const tokenResponse = await this.getOAuthToken(credentials, client);
    return tokenResponse.access_token;
  }

  /**
   * Obtém headers de autenticação
   */
  async getAuthenticatedHeaders(): Promise<Record<string, string>> {
    const token = await this.getAuthenticatedToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Configura autenticação no cliente API
   */
  async setupAuthentication(apiClient: ApiClient): Promise<void> {
    const token = await this.getAuthenticatedToken();
    apiClient.setAuthToken(token);
  }

  /**
   * Renova token de acesso
   */
  async refreshToken(): Promise<string> {
    const credentials = this.getCredentialsFromEnv();
    const client = this.getOAuthClientFromEnv();
    
    // Limpar cache
    this.clearTokenCache();
    
    // Obter novo token
    const tokenResponse = await this.getOAuthToken(credentials, client);
    return tokenResponse.access_token;
  }

  /**
   * Limpa cache de tokens
   */
  clearTokenCache(): void {
    this.tokenCache.clear();
    this.tokenExpiry.clear();
    console.log('Cache de tokens limpo');
  }

  /**
   * Verifica se token está expirado
   */
  isTokenExpired(cacheKey: string): boolean {
    const tokenExpiry = this.tokenExpiry.get(cacheKey);
    return !tokenExpiry || Date.now() >= tokenExpiry;
  }

  /**
   * Obtém informações do token em cache
   */
  getCachedTokenInfo(cacheKey: string): { token: OAuthTokenResponse | null; isExpired: boolean } {
    const token = this.tokenCache.get(cacheKey) || null;
    const isExpired = this.isTokenExpired(cacheKey);
    
    return { token, isExpired };
  }

  /**
   * Valida token de acesso
   */
  async validateToken(token: string): Promise<boolean> {
    try {
      // Fazer uma requisição simples para validar o token
      const tempClient = new ApiClient(process.env.API_BASE_URL || 'https://qa-automacao.digisac.chat/api/v1');
      tempClient.setAuthToken(token);
      
      // Tentar acessar endpoint que requer autenticação
      const response = await tempClient.get('/me/tokens');
      
      return response.status === 200;
    } catch (error) {
      console.error('Erro ao validar token:', error);
      return false;
    }
  }

  /**
   * Obtém informações do usuário autenticado
   */
  async getCurrentUser(): Promise<any> {
    const token = await this.getAuthenticatedToken();
    const tempClient = new ApiClient(process.env.API_BASE_URL || 'https://qa-automacao.digisac.chat/api/v1');
    tempClient.setAuthToken(token);
    
    const response = await tempClient.get('/api/v1/me/tokens');
    
    if (response.status !== 200) {
      throw new Error(`Falha ao obter informações do usuário: ${response.status}`);
    }
    
    return response.body;
  }

  /**
   * Logout (limpa cache)
   */
  async logout(): Promise<void> {
    this.clearTokenCache();
    console.log('Logout realizado com sucesso');
  }

  /**
   * Obtém estatísticas de autenticação
   */
  getAuthStats(): { cachedTokens: number; expiredTokens: number } {
    const cachedTokens = this.tokenCache.size;
    let expiredTokens = 0;
    
    for (const [key] of this.tokenCache) {
      if (this.isTokenExpired(key)) {
        expiredTokens++;
      }
    }
    
    return { cachedTokens, expiredTokens };
  }
}
