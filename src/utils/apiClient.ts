/**
 * Cliente HTTP baseado em Supertest para testes da API Digisac
 * Migrado do Playwright para Jest + Supertest
 */

import request from 'supertest';
import { Response } from 'supertest';
import { ApiResponse, ApiError } from '../types/common.types';

export class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private timeout: number;
  private retries: number;
  private retryDelay: number;

  constructor(
    baseUrl: string,
    defaultHeaders: Record<string, string> = {},
    timeout: number = 30000,
    retries: number = 3,
    retryDelay: number = 1000
  ) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...defaultHeaders
    };
    this.timeout = timeout;
    this.retries = retries;
    this.retryDelay = retryDelay;
  }

  /**
   * Configura headers de autenticação
   */
  setAuthToken(token: string): void {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Remove headers de autenticação
   */
  clearAuthToken(): void {
    delete this.defaultHeaders['Authorization'];
  }

  /**
   * Adiciona header personalizado
   */
  setHeader(key: string, value: string): void {
    this.defaultHeaders[key] = value;
  }

  /**
   * Remove header personalizado
   */
  removeHeader(key: string): void {
    delete this.defaultHeaders[key];
  }

  /**
   * Executa requisição GET
   */
  async get(endpoint: string, headers: Record<string, string> = {}): Promise<Response> {
    return this.request('GET', endpoint, { headers });
  }

  /**
   * Executa requisição POST
   */
  async post(endpoint: string, data?: any, headers: Record<string, string> = {}): Promise<Response> {
    return this.request('POST', endpoint, { data, headers });
  }

  /**
   * Executa requisição PUT
   */
  async put(endpoint: string, data?: any, headers: Record<string, string> = {}): Promise<Response> {
    return this.request('PUT', endpoint, { data, headers });
  }

  /**
   * Executa requisição PATCH
   */
  async patch(endpoint: string, data?: any, headers: Record<string, string> = {}): Promise<Response> {
    return this.request('PATCH', endpoint, { data, headers });
  }

  /**
   * Executa requisição DELETE
   */
  async delete(endpoint: string, headers: Record<string, string> = {}): Promise<Response> {
    return this.request('DELETE', endpoint, { headers });
  }

  /**
   * Executa requisição com retry automático
   */
  private async request(
    method: string,
    endpoint: string,
    options: { data?: any; headers?: Record<string, string> } = {}
  ): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = { ...this.defaultHeaders, ...options.headers };

    let lastError: any;
    
    for (let attempt = 1; attempt <= this.retries; attempt++) {
      try {
        const response = await this.executeRequest(method, url, options.data, headers);
        
        // Se a resposta for bem-sucedida ou erro 4xx, não retry
        if (response.status < 500) {
          return response;
        }
        
        // Se for erro 5xx, pode tentar novamente
        if (attempt < this.retries) {
          console.warn(`Tentativa ${attempt} falhou com status ${response.status}, tentando novamente em ${this.retryDelay}ms...`);
          await this.delay(this.retryDelay * attempt);
        }
        
        lastError = new Error(`HTTP ${response.status}: ${response.text}`);
      } catch (error) {
        lastError = error;
        
        if (attempt < this.retries) {
          console.warn(`Tentativa ${attempt} falhou: ${error instanceof Error ? error.message : String(error)}, tentando novamente em ${this.retryDelay}ms...`);
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    throw lastError;
  }

  /**
   * Executa a requisição HTTP
   */
  private async executeRequest(
    method: string,
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<Response> {
    const agent = request(url);
    let req: any;
    
    // Executar requisição baseada no método
    switch (method.toUpperCase()) {
      case 'GET':
        req = agent.get('');
        break;
      case 'POST':
        req = agent.post('');
        break;
      case 'PUT':
        req = agent.put('');
        break;
      case 'PATCH':
        req = agent.patch('');
        break;
      case 'DELETE':
        req = agent.delete('');
        break;
      default:
        throw new Error(`Método HTTP não suportado: ${method}`);
    }
    
    // Configurar headers
    if (headers) {
      Object.entries(headers).forEach(([key, value]) => {
        req = req.set(key, value);
      });
    }
    
    // Enviar dados se for POST, PUT ou PATCH
    if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      req = req.send(data);
    }
    
    return req;
  }

  /**
   * Executa requisição com query parameters
   */
  async getWithQuery(endpoint: string, query: Record<string, any>, headers: Record<string, string> = {}): Promise<Response> {
    const queryString = new URLSearchParams(query).toString();
    const fullEndpoint = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.get(fullEndpoint, headers);
  }

  /**
   * Valida resposta da API
   */
  validateResponse(response: Response, expectedStatus: number): void {
    if (response.status !== expectedStatus) {
      throw new Error(
        `Status esperado: ${expectedStatus}, recebido: ${response.status}. ` +
        `Resposta: ${JSON.stringify(response.body)}`
      );
    }
  }

  /**
   * Valida estrutura da resposta
   */
  validateResponseStructure(response: Response, requiredFields: string[]): void {
    const body = response.body;
    
    for (const field of requiredFields) {
      if (!(field in body)) {
        throw new Error(`Campo obrigatório '${field}' não encontrado na resposta`);
      }
    }
  }

  /**
   * Valida tempo de resposta
   */
  validateResponseTime(response: Response, maxTime: number): void {
    // Supertest não fornece timing direto, mas podemos usar Date.now()
    // Para timing mais preciso, seria necessário usar outras bibliotecas
    console.warn('Validação de tempo de resposta não implementada para Supertest');
  }

  /**
   * Converte resposta para ApiResponse
   */
  toApiResponse<T>(response: Response): ApiResponse<T> {
    return {
      data: response.body,
      success: response.status >= 200 && response.status < 300
    };
  }

  /**
   * Converte resposta de erro para ApiError
   */
  toApiError(response: Response, endpoint: string): ApiError {
    return {
      error: response.body.error || 'Unknown Error',
      message: response.body.message || 'An error occurred',
      statusCode: response.status,
      timestamp: new Date().toISOString(),
      path: endpoint
    };
  }

  /**
   * Delay para retry
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Obtém informações da requisição
   */
  getRequestInfo(): { baseUrl: string; headers: Record<string, string>; timeout: number } {
    return {
      baseUrl: this.baseUrl,
      headers: { ...this.defaultHeaders },
      timeout: this.timeout
    };
  }
}