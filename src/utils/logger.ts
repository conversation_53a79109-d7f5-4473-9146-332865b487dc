/**
 * Sistema de logging simples com injeção de dependência manual
 */

export interface ILogger {
  debug(message: string, context?: any): void;
  info(message: string, context?: any): void;
  warn(message: string, context?: any): void;
  error(message: string, context?: any): void;
}

export class ConsoleLogger implements ILogger {
  private context: string;

  constructor(context: string = 'App') {
    this.context = context;
  }

  debug(message: string, context?: any): void {
    if (process.env.DEBUG_MODE === 'true') {
      console.log(`🐛 [${this.context}] ${message}`, context ? JSON.stringify(context, null, 2) : '');
    }
  }

  info(message: string, context?: any): void {
    console.log(`ℹ️  [${this.context}] ${message}`, context ? JSON.stringify(context, null, 2) : '');
  }

  warn(message: string, context?: any): void {
    console.warn(`⚠️  [${this.context}] ${message}`, context ? JSON.stringify(context, null, 2) : '');
  }

  error(message: string, context?: any): void {
    console.error(`❌ [${this.context}] ${message}`, context ? JSON.stringify(context, null, 2) : '');
  }
}

export class TestLogger implements ILogger {
  private logs: Array<{ level: string; message: string; context?: any; timestamp: Date }> = [];
  private context: string;

  constructor(context: string = 'Test') {
    this.context = context;
  }

  debug(message: string, context?: any): void {
    this.logs.push({ level: 'debug', message, context, timestamp: new Date() });
    if (process.env.DEBUG_MODE === 'true') {
      console.log(`🐛 [${this.context}] ${message}`, context ? JSON.stringify(context, null, 2) : '');
    }
  }

  info(message: string, context?: any): void {
    this.logs.push({ level: 'info', message, context, timestamp: new Date() });
    console.log(`ℹ️  [${this.context}] ${message}`, context ? JSON.stringify(context, null, 2) : '');
  }

  warn(message: string, context?: any): void {
    this.logs.push({ level: 'warn', message, context, timestamp: new Date() });
    console.warn(`⚠️  [${this.context}] ${message}`, context ? JSON.stringify(context, null, 2) : '');
  }

  error(message: string, context?: any): void {
    this.logs.push({ level: 'error', message, context, timestamp: new Date() });
    console.error(`❌ [${this.context}] ${message}`, context ? JSON.stringify(context, null, 2) : '');
  }

  getLogs(): Array<{ level: string; message: string; context?: any; timestamp: Date }> {
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
  }

  getLogsByLevel(level: string): Array<{ level: string; message: string; context?: any; timestamp: Date }> {
    return this.logs.filter(log => log.level === level);
  }
}

// Service Locator simples para DI manual
export class ServiceContainer {
  private static services: Map<string, any> = new Map();

  static register<T>(key: string, service: T): void {
    this.services.set(key, service);
  }

  static get<T>(key: string): T {
    const service = this.services.get(key);
    if (!service) {
      throw new Error(`Service '${key}' not found in container`);
    }
    return service;
  }

  static has(key: string): boolean {
    return this.services.has(key);
  }

  static clear(): void {
    this.services.clear();
  }
}

// Factory para criar loggers
export class LoggerFactory {
  static createLogger(context: string, isTest: boolean = false): ILogger {
    if (isTest) {
      return new TestLogger(context);
    }
    return new ConsoleLogger(context);
  }

  static createAndRegister(context: string, isTest: boolean = false): ILogger {
    const logger = this.createLogger(context, isTest);
    ServiceContainer.register(`logger:${context}`, logger);
    return logger;
  }

  static getLogger(context: string): ILogger {
    const key = `logger:${context}`;
    if (ServiceContainer.has(key)) {
      return ServiceContainer.get<ILogger>(key);
    }
    
    // Se não existe, cria um novo
    return this.createAndRegister(context, process.env.NODE_ENV === 'test');
  }
}
