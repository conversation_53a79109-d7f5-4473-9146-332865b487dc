/**
 * Gerador de dados de teste para API Digisac
 * Baseado em Faker.js para geração de dados realistas
 */

import { faker } from '@faker-js/faker';
import { Response } from 'supertest';
import { 
  CreateServiceRequest, 
  CreateContactRequest, 
  CreateTicketRequest,
  CreateDepartmentRequest,
  CreateUserRequest,
  CreateTagRequest,
  CreatePersonRequest,
  CreateCampaignRequest,
  CreateQuestionRequest,
  CreateCustomFieldRequest,
  CreateRoleRequest,
  CreateEntityRequest,
  CreateHolidayRequest,
  CreateQuickReplyRequest,
  CreateBotRequest,
  CreateScheduleRequest
} from '../types/api.types';
import { API_ENDPOINTS } from '@/config/apiEndpoints';
import { ApiClient } from './apiClient';

export class DataGenerator {
  static baseUrl = process.env.API_BASE_URL || 'https://qa-automacao.digisac.chat/api/v1';
  static apiClient = new ApiClient(DataGenerator.baseUrl);

  /**
   * Configura o token de autenticação no ApiClient do DataGenerator
   * @param token - Token de autenticação
   */
  static setAuthToken(token: string): void {
    DataGenerator.apiClient.setAuthToken(token);
  }

  /**
   * Gera dados válidos para criação de serviço (conexão)
   */
  static generateServiceData(): CreateServiceRequest {
    return {
      name: faker.company.name() + ' - ' + faker.internet.domainName(),
      type: faker.helpers.arrayElement(['whatsapp', 'telegram', 'email', 'sms']),
      serviceToken: faker.string.alphanumeric(40),
      webHook: faker.internet.url(),
      max_count: faker.number.int({ min: 1, max: 100 })
    };
  }

  /**
   * Gera dados inválidos para criação de serviço
   */
  static generateInvalidServiceData(): Partial<CreateServiceRequest> {
    return {
      name: '', // Nome vazio
      type: 'invalid_type' as any,
      serviceToken: '', // Token vazio
      max_count: -1 // Valor negativo
    };
  }

  /**
   * Gera dados válidos para criação de contato
   */
  static generateContactData(serviceId: string): CreateContactRequest {
    return {
      internalName: faker.person.fullName(),
      number: faker.phone.number(),
      serviceId,
      defaultDepartmentId: undefined,
      tagIds: [],
      customFields: []
    };
  }

  /**
   * Gera dados inválidos para criação de contato
   */
  static generateInvalidContactData(): Partial<CreateContactRequest> {
    return {
      internalName: '', // Nome vazio
      number: 'invalid-phone', // Número inválido
      serviceId: '' // ServiceId vazio
    };
  }

  /**
   * Gera dados válidos para criação de ticket
   */
  static generateTicketData(contactId: string, serviceId: string, departmentId: string): CreateTicketRequest {
    return {
      name: faker.lorem.sentence(),
      contactId,
      serviceId,
      departmentId,
      botId: undefined,
      isRead: false,
      inTrash: false,
      tagsIds: []
    };
  }

  /**
   * Gera dados inválidos para criação de ticket
   */
  static generateInvalidTicketData(): Partial<CreateTicketRequest> {
    return {
      name: '', // Nome vazio
      contactId: '', // ContactId vazio
      serviceId: '', // ServiceId vazio
      departmentId: '' // DepartmentId vazio
    };
  }

  /**
   * Gera dados válidos para criação de departamento
   */
  static generateDepartmentData(): CreateDepartmentRequest {
    return {
      name: 'Department ' + faker.company.buzzNoun() + ' ' + new Date().toLocaleDateString('pt-BR').replace(/\//g, ''),
    };
  }

  /**
   * Cria um departamento e retorna a resposta completa da requisição
   * @param name - Nome do departamento (opcional, se não fornecido usa generateDepartmentData)
   * @returns Promise<Response> - Resposta completa da requisição
   */
  static async createDepartment(name?: string): Promise<Response> {
    const payload = name ? { name } : DataGenerator.generateDepartmentData();
    const response = await DataGenerator.apiClient.post(API_ENDPOINTS.DEPARTMENTS.BASE, payload);
    console.log('📝 ⬆️ Response body createDepartment:', response.body);
    return response;
  }

  /**
   * Exclui um departamento por ID
   * @param id - ID do departamento a ser excluído
   * @returns Promise<Response> - Resposta da requisição de exclusão
   */
  static async deleteDepartment(id: string): Promise<Response> {
    const response = await DataGenerator.apiClient.delete(API_ENDPOINTS.DEPARTMENTS.BY_ID(id));
    console.log('🗑️ ⬆️ Response body deleteDepartment:', response.body);
    return response;
  }

  /**
   * Cria um departamento temporário para testes e retorna o ID
   * Útil para testes que precisam de um departamento mas não querem gerenciar a limpeza manualmente
   * @param name - Nome do departamento (opcional)
   * @returns Promise<string> - ID do departamento criado
   */
  static async createTestDepartment(name?: string): Promise<string> {
    const response = await DataGenerator.createDepartment(name);
    if (response.status !== 200) {
      throw new Error(`Falha ao criar departamento de teste: ${response.status} - ${JSON.stringify(response.body)}`);
    }
    if (!response.body || !response.body.id) {
      throw new Error('Resposta inválida ao criar departamento de teste');
    }
    return response.body.id;
  }

  /**
   * Gera dados válidos para criação de usuário
   */
  static generateUserData(accountId: string, departmentsId: string[]): CreateUserRequest {
    return {
      accountId,
      email: faker.internet.email(),
      password: faker.internet.password({ length: 12 }),
      name: faker.person.fullName(),
      rolesId: undefined,
      organizationIds: [],
      departmentsId
    };
  }

  /**
   * Gera dados inválidos para criação de usuário
   */
  static generateInvalidUserData(): Partial<CreateUserRequest> {
    return {
      email: 'invalid-email', // Email inválido
      password: '123', // Senha muito curta
      name: '', // Nome vazio
      departmentsId: [] // Array vazio
    };
  }

  /**
   * Gera dados válidos para criação de tag
   */
  static generateTagData(): CreateTagRequest {
    return {
      name: faker.word.adjective() + ' ' + faker.word.noun(),
      color: faker.color.rgb({ format: 'hex' })
    };
  }

  /**
   * Gera dados válidos para criação de pessoa
   */
  static generatePersonData(organizationId?: string): CreatePersonRequest {
    return {
      name: faker.person.fullName(),
      email: faker.internet.email(),
      phone: faker.phone.number(),
      jobTitle: faker.person.jobTitle(),
      organizationId
    };
  }

  /**
   * Gera dados válidos para criação de campanha
   */
  static generateCampaignData(serviceId: string, contactIds: string[]): CreateCampaignRequest {
    return {
      name: faker.company.buzzPhrase() + ' Campaign',
      message: faker.lorem.paragraph(),
      scheduledAt: faker.date.future().toISOString(),
      contactIds,
      serviceId
    };
  }

  /**
   * Gera dados válidos para criação de pergunta de avaliação
   */
  static generateQuestionData(): CreateQuestionRequest {
    return {
      name: faker.lorem.words(3),
      duration: faker.number.int({ min: 30, max: 300 }),
      type: faker.helpers.arrayElement(['NPS', 'CSAT']),
      questionMessage: faker.lorem.sentence() + '?',
      tries: faker.number.int({ min: 1, max: 5 }),
      successMessage: faker.lorem.sentence(),
      invalidMessage: faker.lorem.sentence()
    };
  }

  /**
   * Gera dados válidos para criação de campo personalizado
   */
  static generateCustomFieldData(): CreateCustomFieldRequest {
    return {
      name: faker.word.noun() + ' Field',
      type: faker.helpers.arrayElement(['text', 'number', 'email', 'phone', 'date', 'select']),
      allowed: faker.helpers.arrayElement(['contacts', 'tickets', 'users']),
      options: faker.helpers.arrayElements(['Option 1', 'Option 2', 'Option 3'], { min: 2, max: 5 }),
      required: faker.datatype.boolean()
    };
  }

  /**
   * Gera dados válidos para criação de cargo
   */
  static generateRoleData(): CreateRoleRequest {
    return {
      displayName: faker.person.jobTitle(),
      isAdmin: faker.datatype.boolean(),
      permissions: faker.helpers.arrayElements([
        'read_contacts',
        'write_contacts',
        'read_tickets',
        'write_tickets',
        'read_users',
        'write_users'
      ], { min: 1, max: 4 })
    };
  }

  /**
   * Gera dados válidos para criação de entidade
   */
  static generateEntityData(): CreateEntityRequest {
    return {
      name: faker.word.noun(),
      description: faker.lorem.sentence(),
      values: faker.helpers.arrayElements([
        faker.word.noun(),
        faker.word.adjective(),
        faker.word.verb()
      ], { min: 2, max: 5 })
    };
  }

  /**
   * Gera dados válidos para criação de feriado
   */
  static generateHolidayData(): CreateHolidayRequest {
    return {
      name: faker.lorem.words(2),
      date: faker.date.future().toISOString().split('T')[0]
    };
  }

  /**
   * Gera dados válidos para criação de resposta rápida
   */
  static generateQuickReplyData(departmentIds: string[]): CreateQuickReplyRequest {
    return {
      text: faker.lorem.sentence(),
      departmentIds
    };
  }

  /**
   * Gera dados válidos para criação de bot
   */
  static generateBotData(mainDepartmentId: string, departmentsIds: string[], tagsIds: string[]): CreateBotRequest {
    return {
      name: faker.company.buzzNoun() + ' Bot',
      type: faker.helpers.arrayElement(['whatsapp', 'telegram', 'email', 'sms']),
      description: faker.lorem.sentence(),
      mainDepartmentId,
      departmentsIds,
      tagsIds
    };
  }

  /**
   * Gera dados válidos para criação de horário de atendimento
   */
  static generateScheduleData(departmentIds: string[]): any {
    return {
      name: faker.company.buzzNoun() + ' Schedule',
      status: faker.helpers.arrayElement(['active', 'inactive']),
      days: faker.helpers.arrayElements([
        'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'
      ], { min: 1, max: 7 }),
      startTime: faker.date.recent().toTimeString().split(' ')[0],
      endTime: faker.date.future().toTimeString().split(' ')[0],
      departmentIds
    };
  }

  /**
   * Gera dados de filtro para listagem
   */
  static generateFilterData(): Record<string, any> {
    return {
      page: faker.number.int({ min: 1, max: 10 }),
      perPage: faker.helpers.arrayElement([10, 20, 50, 100]),
      order: faker.helpers.arrayElement(['ASC', 'DESC']),
      orderBy: faker.helpers.arrayElement(['name', 'createdAt', 'updatedAt'])
    };
  }

  /**
   * Gera dados de query complexa
   */
  static generateQueryData(): Record<string, any> {
    return {
      include: [
        { model: 'tags', attributes: ['id', 'name'] },
        { model: 'department', attributes: ['id', 'name'] }
      ],
      where: {
        status: faker.helpers.arrayElement(['active', 'inactive']),
        createdAt: {
          $gte: faker.date.past().toISOString(),
          $lte: faker.date.recent().toISOString()
        }
      }
    };
  }

  /**
   * Gera dados de exportação
   */
  static generateExportData(): Record<string, any> {
    return {
      type: faker.helpers.arrayElement(['comma', 'semiColon']),
      format: faker.helpers.arrayElement(['csv', 'xlsx', 'json']),
      fields: faker.helpers.arrayElements(['id', 'name', 'email', 'createdAt'], { min: 2, max: 4 })
    };
  }

  /**
   * Gera dados de arquivo para upload
   */
  static generateFileData(): { field: string; buffer: Buffer; filename: string; mimetype: string } {
    const fileTypes = [
      { mimetype: 'image/jpeg', extension: 'jpg' },
      { mimetype: 'image/png', extension: 'png' },
      { mimetype: 'application/pdf', extension: 'pdf' },
      { mimetype: 'audio/mpeg', extension: 'mp3' }
    ];
    
    const fileType = faker.helpers.arrayElement(fileTypes);
    const filename = faker.system.fileName();
    
    return {
      field: 'file',
      buffer: Buffer.from(faker.string.alphanumeric(1000)), // Buffer simulado
      filename,
      mimetype: fileType.mimetype
    };
  }

  /**
   * Gera dados de mensagem
   */
  static generateMessageData(number: string, serviceId: string): Record<string, any> {
    const messageTypes = ['text', 'image', 'audio', 'video', 'document'];
    const type = faker.helpers.arrayElement(messageTypes);
    
    const baseData = {
      number,
      serviceId,
      origin: faker.helpers.arrayElement(['bot', 'user'])
    };

    if (type === 'text') {
      return {
        ...baseData,
        text: faker.lorem.sentence()
      };
    } else {
      return {
        ...baseData,
        file: this.generateFileData()
      };
    }
  }

  /**
   * Gera dados de transferência de ticket
   */
  static generateTransferData(departmentId: string, userId: string): Record<string, any> {
    return {
      departmentId,
      userId
    };
  }

  /**
   * Gera dados de auditoria
   */
  static generateAuditData(): Record<string, any> {
    return {
      action: faker.helpers.arrayElement(['create', 'update', 'delete', 'login', 'logout']),
      ipAddress: faker.internet.ip(),
      userAgent: faker.internet.userAgent()
    };
  }

  /**
   * Gera dados de métricas
   */
  static generateMetricData(): Record<string, any> {
    return {
      name: faker.word.noun(),
      value: faker.number.float({ min: 0, max: 100, fractionDigits: 2 }),
      unit: faker.helpers.arrayElement(['ms', 's', 'rps', '%', 'count']),
      timestamp: faker.date.recent().toISOString(),
      tags: {
        environment: faker.helpers.arrayElement(['dev', 'qa', 'prod']),
        service: faker.word.noun()
      }
    };
  }

  /**
   * Gera dados de configuração
   */
  static generateConfigData(): Record<string, any> {
    return {
      key: faker.word.noun() + '_' + faker.word.adjective(),
      value: faker.helpers.arrayElement([
        faker.lorem.word(),
        faker.number.int(),
        faker.datatype.boolean(),
        faker.lorem.sentence()
      ]),
      type: faker.helpers.arrayElement(['string', 'number', 'boolean', 'object']),
      description: faker.lorem.sentence(),
      required: faker.datatype.boolean()
    };
  }

  /**
   * Gera dados de notificação
   */
  static generateNotificationData(): Record<string, any> {
    return {
      title: faker.lorem.words(3),
      message: faker.lorem.sentence(),
      type: faker.helpers.arrayElement(['info', 'warning', 'error', 'success']),
      read: faker.datatype.boolean()
    };
  }

  /**
   * Gera dados de cache
   */
  static generateCacheData<T = any>(value: T): Record<string, any> {
    return {
      key: faker.word.noun() + '_' + faker.string.alphanumeric(8),
      value,
      expiresAt: faker.date.future().getTime(),
      createdAt: faker.date.past().getTime()
    };
  }

  /**
   * Gera dados de log
   */
  static generateLogData(): Record<string, any> {
    return {
      level: faker.helpers.arrayElement(['debug', 'info', 'warn', 'error']),
      message: faker.lorem.sentence(),
      timestamp: faker.date.recent().toISOString(),
      context: {
        userId: faker.string.uuid(),
        requestId: faker.string.uuid(),
        service: faker.word.noun()
      }
    };
  }
}
