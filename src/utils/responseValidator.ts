/**
 * Validador de respostas da API Digisac
 * Migrado do Playwright para Jest + Supertest
 */

import { Response } from 'supertest';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { ApiError, ValidationResult, ValidationError } from '../types/common.types';

export class ResponseValidator {
  private ajv: Ajv;

  constructor() {
    this.ajv = new Ajv({ 
      allErrors: true,
      verbose: true,
      strict: false
    });
    
    // Adicionar suporte a formatos de data
    addFormats(this.ajv);
  }

  /**
   * Valida status HTTP da resposta
   */
  validateStatus(response: Response, expectedStatus: number): void {
    if (response.status !== expectedStatus) {
      const errorMessage = this.formatStatusError(response, expectedStatus);
      throw new Error(errorMessage);
    }
  }

  /**
   * Valida status HTTP com múltiplas opções
   */
  validateStatusOneOf(response: Response, expectedStatuses: number[]): void {
    if (!expectedStatuses.includes(response.status)) {
      const errorMessage = this.formatStatusError(response, expectedStatuses);
      throw new Error(errorMessage);
    }
  }

  /**
   * Valida estrutura da resposta usando schema JSON
   */
  validateResponse(response: Response, schema: any): ValidationResult {
    try {
      const responseData = response.body;
      const validate = this.ajv.compile(schema);
      const isValid = validate(responseData);

      if (!isValid) {
        const errors = this.formatValidationErrors(validate.errors || []);
        return {
          isValid: false,
          errors
        };
      }

      return {
        isValid: true,
        errors: []
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [{
          field: 'response',
          message: `Erro ao validar resposta: ${error instanceof Error ? error.message : String(error)}`,
          value: response.body
        }]
      };
    }
  }

  /**
   * Valida campos obrigatórios na resposta
   */
  validateRequiredFields(response: Response, requiredFields: string[]): ValidationResult {
    const responseData = response.body;
    const errors: ValidationError[] = [];

    for (const field of requiredFields) {
      if (!this.hasProperty(responseData, field)) {
        errors.push({
          field,
          message: `Campo obrigatório '${field}' não encontrado na resposta`,
          value: undefined
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida tipos de dados na resposta
   */
  validateDataTypes(response: Response, fieldTypes: Record<string, string>): ValidationResult {
    const responseData = response.body;
    const errors: ValidationError[] = [];

    for (const [field, expectedType] of Object.entries(fieldTypes)) {
      if (this.hasProperty(responseData, field)) {
        const actualType = this.getType(responseData[field]);
        if (actualType !== expectedType) {
          errors.push({
            field,
            message: `Tipo esperado: ${expectedType}, recebido: ${actualType}`,
            value: responseData[field]
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida formato de email
   */
  validateEmailFormat(response: Response, emailFields: string[]): ValidationResult {
    const responseData = response.body;
    const errors: ValidationError[] = [];
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    for (const field of emailFields) {
      if (this.hasProperty(responseData, field)) {
        const email = responseData[field];
        if (typeof email === 'string' && !emailRegex.test(email)) {
          errors.push({
            field,
            message: `Formato de email inválido: ${email}`,
            value: email
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida formato de UUID
   */
  validateUUIDFormat(response: Response, uuidFields: string[]): ValidationResult {
    const responseData = response.body;
    const errors: ValidationError[] = [];
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

    for (const field of uuidFields) {
      if (this.hasProperty(responseData, field)) {
        const uuid = responseData[field];
        if (typeof uuid === 'string' && !uuidRegex.test(uuid)) {
          errors.push({
            field,
            message: `Formato de UUID inválido: ${uuid}`,
            value: uuid
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida formato de data ISO 8601
   */
  validateDateFormat(response: Response, dateFields: string[]): ValidationResult {
    const responseData = response.body;
    const errors: ValidationError[] = [];

    for (const field of dateFields) {
      if (this.hasProperty(responseData, field)) {
        const date = responseData[field];
        if (typeof date === 'string') {
          const dateObj = new Date(date);
          if (isNaN(dateObj.getTime()) || dateObj.toISOString() !== date) {
            errors.push({
              field,
              message: `Formato de data inválido (esperado ISO 8601): ${date}`,
              value: date
            });
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida estrutura de erro da API
   */
  validateErrorResponse(response: Response): ValidationResult {
    const errorSchema = {
      type: 'object',
      properties: {
        error: { type: 'string' },
        message: { type: 'string' },
        statusCode: { type: 'number' },
        timestamp: { type: 'string' }
      },
      required: ['error', 'message', 'statusCode', 'timestamp']
    };

    return this.validateResponse(response, errorSchema);
  }

  /**
   * Valida estrutura de resposta paginada
   */
  validatePaginatedResponse(response: Response, itemSchema?: any): ValidationResult {
    const paginatedSchema = {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: itemSchema || { type: 'object' }
        },
        total: { type: 'number' },
        page: { type: 'number' },
        perPage: { type: 'number' }
      },
      required: ['data', 'total', 'page', 'perPage']
    };

    return this.validateResponse(response, paginatedSchema);
  }

  /**
   * Valida tempo de resposta
   */
  validateResponseTime(response: Response, maxTime: number): void {
    // Supertest não fornece timing direto
    // Para timing preciso, seria necessário usar outras bibliotecas
    console.warn('Validação de tempo de resposta não implementada para Supertest');
  }

  /**
   * Valida headers da resposta
   */
  validateHeaders(response: Response, expectedHeaders: Record<string, string>): ValidationResult {
    const errors: ValidationError[] = [];

    for (const [header, expectedValue] of Object.entries(expectedHeaders)) {
      const actualValue = response.headers[header.toLowerCase()];
      if (actualValue !== expectedValue) {
        errors.push({
          field: `header.${header}`,
          message: `Header esperado: ${expectedValue}, recebido: ${actualValue}`,
          value: actualValue
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida se resposta contém array
   */
  validateArray(response: Response, field: string, minLength?: number): ValidationResult {
    const responseData = response.body;
    const errors: ValidationError[] = [];

    if (!this.hasProperty(responseData, field)) {
      errors.push({
        field,
        message: `Campo '${field}' não encontrado na resposta`,
        value: undefined
      });
    } else if (!Array.isArray(responseData[field])) {
      errors.push({
        field,
        message: `Campo '${field}' não é um array`,
        value: responseData[field]
      });
    } else if (minLength && responseData[field].length < minLength) {
      errors.push({
        field,
        message: `Array '${field}' deve ter pelo menos ${minLength} itens, encontrado: ${responseData[field].length}`,
        value: responseData[field]
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida se resposta contém objeto
   */
  validateObject(response: Response, field: string): ValidationResult {
    const responseData = response.body;
    const errors: ValidationError[] = [];

    if (!this.hasProperty(responseData, field)) {
      errors.push({
        field,
        message: `Campo '${field}' não encontrado na resposta`,
        value: undefined
      });
    } else if (typeof responseData[field] !== 'object' || Array.isArray(responseData[field])) {
      errors.push({
        field,
        message: `Campo '${field}' não é um objeto`,
        value: responseData[field]
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Utilitários privados
   */
  private hasProperty(obj: any, path: string): boolean {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj) !== undefined;
  }

  private getType(value: any): string {
    if (value === null) return 'null';
    if (Array.isArray(value)) return 'array';
    return typeof value;
  }

  private formatStatusError(response: Response, expectedStatus: number | number[]): string {
    const expected = Array.isArray(expectedStatus) 
      ? expectedStatus.join(' ou ') 
      : expectedStatus;
    
    return `Status HTTP esperado: ${expected}, recebido: ${response.status}. ` +
           `Resposta: ${JSON.stringify(response.body)}`;
  }

  private formatValidationErrors(errors: any[]): ValidationError[] {
    return errors.map(error => ({
      field: error.instancePath || error.dataPath || 'root',
      message: error.message || 'Erro de validação',
      value: error.data
    }));
  }
}
