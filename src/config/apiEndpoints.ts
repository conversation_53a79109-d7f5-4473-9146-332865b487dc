/**
 * Configurações de endpoints da API Digisac
 * Baseado na documentação oficial: digisac-api-doc.md
 */

export const API_ENDPOINTS = {
  // ===== AUTENTICAÇÃO =====
  AUTH: {
    TOKEN: '/oauth/token',
    REFRESH: '/oauth/token/refresh',
    LOGOUT: '/oauth/logout'
  },

  // ===== AGENDAMENTOS =====
  SCHEDULE: {
    BASE: '/schedule',
    BY_ID: (id: string) => `/schedule/${id}`,
    LIST: '/schedule?perPage=40'
  },

  // ===== TEMPO REAL =====
  NOW: {
    DEPARTMENTS_RESUME: '/now/departments-resume',
    ATTENDANCE_RESUME: '/now/attendance-resume',
    RESUME: '/now/resume'
  },

  // ===== ASSUNTOS DE CHAMADO =====
  TICKET_TOPICS: {
    BASE: '/ticket-topics',
    BY_ID: (id: string) => `/ticket-topics/${id}`,
    LIST: '/ticket-topics?perPage=40'
  },

  // ===== AUDITORIA =====
  AUTH_HISTORY: {
    BASE: '/auth-history',
    BY_ID: (id: string) => `/auth-history/${id}`,
    LIST: '/auth-history?perPage=40'
  },

  // ===== AVALIAÇÕES =====
  QUESTIONS: {
    BASE: '/questions',
    BY_ID: (id: string) => `/questions/${id}`,
    LIST: '/questions?perPage=40'
  },

  // ===== CAMPANHAS =====
  CAMPAIGNS: {
    BASE: '/campaigns',
    BY_ID: (id: string) => `/campaigns/${id}`,
    STATS: (id: string) => `/campaigns/${id}/stats`,
    EXPORT_CSV: '/campaigns/export/csv',
    LIST: '/campaigns?perPage=40'
  },

  // ===== CAMPOS PERSONALIZADOS =====
  CUSTOM_FIELDS: {
    BASE: '/custom-fields',
    BY_ID: (id: string) => `/custom-fields/${id}`,
    LIST: '/custom-fields?perPage=40'
  },

  // ===== CARGOS =====
  ROLES: {
    BASE: '/roles',
    BY_ID: (id: string) => `/roles/${id}`,
    WITH_PERMISSIONS: (id: string) => `/roles/${id}?include=permissions`,
    LIST: '/roles?perPage=40'
  },

  // ===== CONEXÕES/SERVICES =====
  SERVICES: {
    BASE: '/services',
    BY_ID: (id: string) => `/services/${id}`,
    RESTART: (id: string) => `/services/${id}/restart`,
    LOGOUT: (id: string) => `/services/${id}/logout`,
    LIST: '/services?perPage=40'
  },

  // ===== CONTATOS =====
  CONTACTS: {
    BASE: '/contacts',
    BY_ID: (id: string) => `/contacts/${id}`,
    MANY: '/contacts/many',
    EXPORT_CSV: '/contacts/export/csv',
    COUNT: '/contacts/count',
    WITH_TAGS: (id: string) => `/contacts/${id}?include[0]=tags&include[1]=`,
    WITH_CUSTOM_FIELDS: (fieldName: string) => `/contacts?query={"include":[{"model":"customFieldValues","include":[{"model":"customField","where":{"name":"${fieldName}"}}]}]}`,
    WITH_ORGANIZATION: (organizationId: string) => `/contacts?query={"include":[{"model":"person","required":"true","where":{"id":"${organizationId}"}}],"perPage":"30"}`,
    WITH_PERSON: (personId: string) => `/contacts?query={"include":[{"model":"person","required":"true","where":{"id":"${personId}"}}],"perPage":"30"}`,
    BY_SERVICE: (serviceId: string) => `/contacts?where[serviceId]=${serviceId}&perPage=40`,
    LIST: '/contacts?perPage=40'
  },

  // ===== DEPARTAMENTOS =====
  DEPARTMENTS: {
    BASE: '/departments',
    BY_ID: (id: string) => `/departments/${id}`,
    USERS: (id: string) => `/departments/${id}/users?perPage=40`,
    LIST: '/departments?perPage=40'
  },

  // ===== ENTIDADES =====
  ENTITIES: {
    BASE: '/entities',
    BY_ID: (id: string) => `/entities/${id}`,
    LIST: '/entities?perPage=40'
  },

  // ===== FERIADOS =====
  HOLIDAYS: {
    BASE: '/holidays',
    BY_ID: (id: string) => `/holidays/${id}`,
    LIST: '/holidays?perPage=40'
  },

  // ===== FILAS =====
  QUEUE: {
    BASE: '/queue',
    LIST: '/queue?perPage=40'
  },

  // ===== HISTÓRICO DE CONVERSAS =====
  CHAT_HISTORY: {
    EXPORT_CSV: '/chat-history/export/csv'
  },

  // ===== MÉTRICAS =====
  METRICS: {
    BY_ID: (id: string) => `/metrics/${id}`
  },

  // ===== MENSAGENS =====
  MESSAGES: {
    BASE: '/messages',
    BY_ID: (id: string) => `/messages/${id}`,
    BY_TICKET: (ticketId: string) => `/messages?where[ticketId]=${ticketId}&perPage=40`
  },

  // ===== PESSOAS =====
  PERSONS: {
    BASE: '/persons',
    BY_ID: (id: string) => `/persons/${id}`,
    WITH_CONTACTS: (id: string) => `/persons/${id}?include=contacts`,
    LIST: '/persons?perPage=40'
  },

  // ===== RESPOSTAS RÁPIDAS =====
  QUICK_REPLIES: {
    BASE: '/quick-replies',
    BY_ID: (id: string) => `/quick-replies/${id}`,
    WITH_DEPARTMENTS: '/quick-replies?query={"attributes":["id","text"],"include":[{"model":"departments","attributes":["name","id"],"through":{"attributes":[]}}],"perPage":40}',
    LIST: '/quick-replies?perPage=40'
  },

  // ===== ROBÔS =====
  BOTS: {
    BASE: '/bots',
    BY_ID: (id: string) => `/bots/${id}`,
    BY_DEPARTMENT: (departmentId: string) => `/bots?where[departmentId]=${departmentId}`,
    LIST: '/bots?perPage=40'
  },

  // ===== TAGS =====
  TAGS: {
    BASE: '/tags',
    BY_ID: (id: string) => `/tags/${id}`,
    LIST: '/tags?perPage=40'
  },

  // ===== TICKETS =====
  TICKETS: {
    BASE: '/tickets',
    BY_ID: (id: string) => `/tickets/${id}`,
    MESSAGES: (id: string) => `/tickets/${id}/messages`,
    CLOSE: (id: string) => `/tickets/${id}/close`,
    TRASH: (id: string) => `/tickets/${id}/trash`,
    RESTORE: (id: string) => `/tickets/${id}/restore`,
    TRANSFER: (id: string) => `/tickets/${id}/transfer`,
    BY_TAG: (tagName: string) => `/tickets?query={"include":[{"model":"tags","required":true,"where":{"name":"${tagName}"}}]}`,
    LIST: '/tickets?perPage=40'
  },

  // ===== TOKENS =====
  TOKENS: {
    BASE: '/me/tokens',
    BY_ID: (id: string) => `/me/tokens/${id}`,
    LIST: '/me/tokens?perPage=40'
  },

  // ===== USUÁRIOS =====
  USERS: {
    BASE: '/users',
    BY_ID: (id: string) => `/users/${id}`,
    WITH_ROLES: (id: string) => `/users/${id}?include=roles`,
    LIST: '/users?perPage=40'
  },

  // ===== HORÁRIOS DE ATENDIMENTO =====
  SCHEDULES: {
    BASE: '/schedules',
    BY_ID: (id: string) => `/schedules/${id}`,
    LIST: '/schedules?perPage=40'
  },

  // ===== REDEFINIR SENHA =====
  RESET_PASSWORD: {
    REQUEST: '/reset-password/request',
    TOKEN: '/reset-password/token'
  }
} as const;

// ===== CONFIGURAÇÕES DE PAGINAÇÃO =====
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE: 1,
  DEFAULT_PER_PAGE: 40,
  MAX_PER_PAGE: 100,
  MIN_PER_PAGE: 1
} as const;

// ===== CONFIGURAÇÕES DE FILTROS =====
export const FILTER_CONFIG = {
  SUPPORTED_OPERATORS: ['$eq', '$ne', '$gt', '$gte', '$lt', '$lte', '$in', '$nin', '$like', '$ilike'],
  SUPPORTED_ORDERS: ['ASC', 'DESC'],
  SUPPORTED_FIELDS: ['name', 'email', 'createdAt', 'updatedAt', 'status']
} as const;

// ===== CONFIGURAÇÕES DE EXPORTAÇÃO =====
export const EXPORT_CONFIG = {
  SUPPORTED_TYPES: ['comma', 'semiColon'],
  SUPPORTED_FORMATS: ['csv', 'xlsx', 'json'],
  MAX_RECORDS: 10000
} as const;

// ===== CONFIGURAÇÕES DE TIMEOUT =====
export const TIMEOUT_CONFIG = {
  DEFAULT: 30000,
  UPLOAD: 60000,
  EXPORT: 120000,
  AUTH: 10000
} as const;

// ===== CONFIGURAÇÕES DE RETRY =====
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  BACKOFF_MULTIPLIER: 2,
  MAX_RETRY_DELAY: 10000
} as const;

// ===== CONFIGURAÇÕES DE RATE LIMITING =====
export const RATE_LIMIT_CONFIG = {
  REQUESTS_PER_HOUR: 1000,
  REQUESTS_PER_MINUTE: 100,
  BURST_LIMIT: 10
} as const;

// ===== CONFIGURAÇÕES DE VALIDAÇÃO =====
export const VALIDATION_CONFIG = {
  MAX_STRING_LENGTH: 255,
  MAX_TEXT_LENGTH: 10000,
  MAX_ARRAY_LENGTH: 1000,
  MAX_OBJECT_DEPTH: 10
} as const;

// ===== CONFIGURAÇÕES DE CACHE =====
export const CACHE_CONFIG = {
  TOKEN_TTL: 3600, // 1 hora
  DATA_TTL: 300, // 5 minutos
  MAX_CACHE_SIZE: 1000
} as const;

// ===== CONFIGURAÇÕES DE LOGGING =====
export const LOGGING_CONFIG = {
  LEVELS: ['debug', 'info', 'warn', 'error'],
  MAX_LOG_SIZE: 10485760, // 10MB
  MAX_LOG_FILES: 5
} as const;

// ===== CONFIGURAÇÕES DE PERFORMANCE =====
export const PERFORMANCE_CONFIG = {
  MAX_RESPONSE_TIME: 2000, // 2 segundos
  MAX_MEMORY_USAGE: 100 * 1024 * 1024, // 100MB
  MAX_CPU_USAGE: 80 // 80%
} as const;
