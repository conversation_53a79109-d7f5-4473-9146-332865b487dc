/**
 * Schemas de validação para API Digisac
 * Baseado na documentação oficial: digisac-api-doc.md
 */

export const VALIDATION_SCHEMAS = {
  // ===== SCHEMAS BÁSICOS =====
  ERROR_RESPONSE: {
    type: 'object',
    properties: {
      error: { type: 'string' },
      message: { type: 'string' },
      statusCode: { type: 'number' },
      timestamp: { type: 'string', format: 'date-time' },
      path: { type: 'string' }
    },
    required: ['error', 'message', 'statusCode', 'timestamp']
  },

  SUCCESS_RESPONSE: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      data: { type: 'object' }
    },
    required: ['success', 'data']
  },

  PAGINATED_RESPONSE: {
    type: 'object',
    properties: {
      data: { type: 'array' },
      total: { type: 'number' },
      limit: { type: 'number' },
      skip: { type: 'number' },
      currentPage: { type: 'number' },
      lastPage: { type: 'number' },
      from: { type: 'number' },
      to: { type: 'number' }
    },
    required: ['data', 'total', 'limit', 'skip', 'currentPage', 'lastPage', 'from', 'to']
  },

  // ===== AGENDAMENTOS (Schedule) =====
  SCHEDULE: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      contactId: { type: 'string' },
      departmentId: { type: 'string' },
      files: { type: 'array' },
      message: { type: 'string' },
      notes: { type: 'string' },
      notificateUser: { type: 'boolean' },
      openTicket: { type: 'boolean' },
      scheduledAt: { type: 'string', format: 'date-time' },
      userId: { type: 'string' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'contactId', 'departmentId', 'message', 'notificateUser', 'openTicket', 'scheduledAt', 'userId']
  },

  CREATE_SCHEDULE_REQUEST: {
    type: 'object',
    properties: {
      contactId: { type: 'string' },
      departmentId: { type: 'string' },
      files: { type: 'array' },
      message: { type: 'string' },
      notes: { type: 'string' },
      notificateUser: { type: 'boolean' },
      openTicket: { type: 'boolean' },
      scheduledAt: { type: 'string', format: 'date-time' },
      userId: { type: 'string' }
    },
    required: ['contactId', 'departmentId', 'message', 'notificateUser', 'openTicket', 'scheduledAt', 'userId']
  },

  // ===== TELA AGORA (Now) =====
  DEPARTMENTS_RESUME: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      activeUsers: { type: 'number' },
      totalUsers: { type: 'number' },
      openTickets: { type: 'number' },
      closedTickets: { type: 'number' }
    },
    required: ['id', 'name', 'activeUsers', 'totalUsers', 'openTickets', 'closedTickets']
  },

  ATTENDANCE_RESUME: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      email: { type: 'string' },
      departmentId: { type: 'string' },
      departmentName: { type: 'string' },
      status: { type: 'string', enum: ['online', 'offline', 'busy'] },
      openTickets: { type: 'number' },
      closedTickets: { type: 'number' }
    },
    required: ['id', 'name', 'email', 'departmentId', 'departmentName', 'status', 'openTickets', 'closedTickets']
  },

  // ===== ASSUNTOS DE CHAMADO (Ticket Topics) =====
  TICKET_TOPIC: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name']
  },

  CREATE_TICKET_TOPIC_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' }
    },
    required: ['name']
  },

  // ===== AUDITORIA (Auth History) =====
  AUTH_HISTORY: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      userId: { type: 'string' },
      userName: { type: 'string' },
      action: { type: 'string' },
      ipAddress: { type: 'string' },
      userAgent: { type: 'string' },
      createdAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'userId', 'userName', 'action', 'ipAddress', 'userAgent', 'createdAt']
  },

  // ===== AVALIAÇÕES (Questions) =====
  QUESTION: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      duration: { type: 'number' },
      type: { type: 'string', enum: ['NPS', 'CSAT'] },
      questionMessage: { type: 'string' },
      tries: { type: 'number' },
      successMessage: { type: 'string' },
      invalidMessage: { type: 'string' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'duration', 'type', 'questionMessage', 'tries', 'successMessage', 'invalidMessage']
  },

  CREATE_QUESTION_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      duration: { type: 'number' },
      type: { type: 'string', enum: ['NPS', 'CSAT'] },
      questionMessage: { type: 'string' },
      tries: { type: 'number' },
      successMessage: { type: 'string' },
      invalidMessage: { type: 'string' }
    },
    required: ['name', 'duration', 'type', 'questionMessage', 'tries', 'successMessage', 'invalidMessage']
  },

  // ===== CAMPANHAS (Campaigns) =====
  CAMPAIGN: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      message: { type: 'string' },
      status: { type: 'string', enum: ['draft', 'scheduled', 'running', 'completed', 'cancelled'] },
      scheduledAt: { type: 'string', format: 'date-time' },
      totalContacts: { type: 'number' },
      sentContacts: { type: 'number' },
      deliveredContacts: { type: 'number' },
      readContacts: { type: 'number' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'message', 'status', 'totalContacts', 'sentContacts', 'deliveredContacts', 'readContacts']
  },

  CREATE_CAMPAIGN_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      message: { type: 'string' },
      scheduledAt: { type: 'string', format: 'date-time' },
      contactIds: { type: 'array', items: { type: 'string' } },
      serviceId: { type: 'string' }
    },
    required: ['name', 'message', 'contactIds', 'serviceId']
  },

  // ===== CAMPOS PERSONALIZADOS (Custom Fields) =====
  CUSTOM_FIELD: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      type: { type: 'string', enum: ['text', 'number', 'email', 'phone', 'date', 'select', 'multiselect'] },
      allowed: { type: 'string', enum: ['contacts', 'tickets', 'users'] },
      options: { type: 'array', items: { type: 'string' } },
      required: { type: 'boolean' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'type', 'allowed']
  },

  CREATE_CUSTOM_FIELD_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      type: { type: 'string', enum: ['text', 'number', 'email', 'phone', 'date', 'select', 'multiselect'] },
      allowed: { type: 'string', enum: ['contacts', 'tickets', 'users'] },
      options: { type: 'array', items: { type: 'string' } },
      required: { type: 'boolean' }
    },
    required: ['name', 'type', 'allowed']
  },

  // ===== CARGOS (Roles) =====
  ROLE: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      displayName: { type: 'string' },
      isAdmin: { type: 'boolean' },
      permissions: { type: 'array', items: { type: 'string' } },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'displayName', 'isAdmin', 'permissions']
  },

  CREATE_ROLE_REQUEST: {
    type: 'object',
    properties: {
      displayName: { type: 'string' },
      isAdmin: { type: 'boolean' },
      permissions: { type: 'array', items: { type: 'string' } }
    },
    required: ['displayName', 'isAdmin', 'permissions']
  },

  // ===== CONEXÕES (Services) =====
  SERVICE: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      type: { type: 'string', enum: ['whatsapp', 'telegram', 'email', 'sms'] },
      token: { type: 'string' },
      archivedAt: { type: 'string', format: 'date-time' },
      data: {
        type: 'object',
        properties: {
          status: {
            type: 'object',
            properties: {
              isStarted: { type: 'boolean' },
              isStarting: { type: 'boolean' },
              isConnected: { type: 'boolean' }
            },
            additionalProperties: true
          },
          webchat: { type: 'object' },
          syncFlowDone: { type: 'boolean' },
          lastShutdownAt: { type: 'string', format: 'date-time' },
          isManuallyDisconnected: { type: 'boolean' }
        },
        required: ['status', 'webchat', 'syncFlowDone', 'isManuallyDisconnected']
      },
      settings: {
        type: 'object',
        properties: {
          readReceipts: { type: 'boolean' },
          reactionsEnabled: { type: 'boolean' },
          blockMessageRulesActive: { type: 'boolean' },
          markComposingBeforeSend: { type: 'boolean' },
          unblockByReceiveMessage: { type: 'boolean' },
          shouldOpenTicketForGroups: { type: 'boolean' }
        }
      },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' },
      deletedAt: { type: ['string', 'null'], format: 'date-time' },
      accountId: { type: 'string' },
      botId: { type: ['string', 'null'] },
      defaultDepartmentId: { type: 'string' },
      health: { type: 'object' },
      isMultiDevice: { type: 'boolean' },
      webchatIdleStage: { type: 'object' }
    },
    required: ['id', 'name', 'type', 'token', 'data', 'settings', 'createdAt', 'updatedAt'],
    additionalProperties: true
  },

  CREATE_SERVICE_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      type: { type: 'string', enum: ['whatsapp', 'telegram', 'email', 'sms'] },
      serviceToken: { type: 'string' },
      webHook: { type: 'string' },
      max_count: { type: 'number' }
    },
    required: ['name', 'type', 'serviceToken', 'max_count']
  },

  // ===== CONTATOS (Contacts) =====
  CONTACT: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      internalName: { type: 'string' },
      number: { type: 'string' },
      serviceId: { type: 'string' },
      defaultDepartmentId: { type: 'string' },
      tagIds: { type: 'array', items: { type: 'string' } },
      customFields: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            value: { type: 'string' }
          },
          required: ['id', 'value']
        }
      },
      personId: { type: 'string' },
      organizationId: { type: 'string' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'internalName', 'number', 'serviceId', 'tagIds', 'customFields']
  },

  CREATE_CONTACT_REQUEST: {
    type: 'object',
    properties: {
      internalName: { type: 'string' },
      number: { type: 'string' },
      serviceId: { type: 'string' },
      defaultDepartmentId: { type: 'string' },
      tagIds: { type: 'array', items: { type: 'string' } },
      customFields: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            value: { type: 'string' }
          },
          required: ['id', 'value']
        }
      }
    },
    required: ['internalName', 'number', 'serviceId']
  },

  // ===== DEPARTAMENTOS (Departments) =====
  DEPARTMENT: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      archivedAt: { type: ['string', 'null'] },
      accountId: { type: 'string' },
      updatedAt: { type: 'string', format: 'date-time' },
      createdAt: { type: 'string', format: 'date-time' },
      distributionId: { type: ['string', 'null'] }
    },
    required: ['id', 'name', 'archivedAt', 'accountId', 'updatedAt', 'createdAt', 'distributionId']
  },

  CREATE_DEPARTMENT_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      defaultRoleId: { type: 'string' },
      isPrivate: { type: 'boolean' },
      tagsIds: { type: 'array', items: { type: 'string' } },
      departmentsId: { type: 'array', items: { type: 'string' } }
    },
    required: ['name', 'isPrivate']
  },

  // ===== ENTIDADES (Entities) =====
  ENTITY: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      description: { type: 'string' },
      values: { type: 'array', items: { type: 'string' } },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'description', 'values']
  },

  CREATE_ENTITY_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      description: { type: 'string' },
      values: { type: 'array', items: { type: 'string' } }
    },
    required: ['name', 'description', 'values']
  },

  // ===== FERIADOS (Holidays) =====
  HOLIDAY: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      date: { type: 'string', format: 'date' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'date']
  },

  CREATE_HOLIDAY_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      date: { type: 'string', format: 'date' }
    },
    required: ['name', 'date']
  },

  // ===== FILAS (Queue) =====
  QUEUE: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      departmentId: { type: 'string' },
      departmentName: { type: 'string' },
      waitingContacts: { type: 'number' },
      averageWaitTime: { type: 'number' },
      createdAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'departmentId', 'departmentName', 'waitingContacts', 'averageWaitTime']
  },

  // ===== MENSAGENS (Messages) =====
  MESSAGE: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      text: { type: 'string' },
      type: { type: 'string', enum: ['text', 'image', 'audio', 'video', 'document', 'location', 'contact', 'sticker'] },
      number: { type: 'string' },
      serviceId: { type: 'string' },
      ticketId: { type: 'string' },
      contactId: { type: 'string' },
      userId: { type: 'string' },
      origin: { type: 'string', enum: ['bot', 'user'] },
      file: {
        type: 'object',
        properties: {
          base64: { type: 'string' },
          mimetype: { type: 'string' },
          name: { type: 'string' }
        },
        required: ['base64', 'mimetype', 'name']
      },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'number', 'serviceId', 'origin']
  },

  CREATE_MESSAGE_REQUEST: {
    type: 'object',
    properties: {
      text: { type: 'string' },
      number: { type: 'string' },
      serviceId: { type: 'string' },
      ticketId: { type: 'string' },
      contactId: { type: 'string' },
      userId: { type: 'string' },
      origin: { type: 'string', enum: ['bot', 'user'] },
      file: {
        type: 'object',
        properties: {
          base64: { type: 'string' },
          mimetype: { type: 'string' },
          name: { type: 'string' }
        },
        required: ['base64', 'mimetype', 'name']
      }
    },
    required: ['number', 'serviceId']
  },

  // ===== PESSOAS (Persons) =====
  PERSON: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      email: { type: 'string' },
      phone: { type: 'string' },
      jobTitle: { type: 'string' },
      organizationId: { type: 'string' },
      contacts: { type: 'array', items: { $ref: '#/definitions/CONTACT' } },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name']
  },

  CREATE_PERSON_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      email: { type: 'string' },
      phone: { type: 'string' },
      jobTitle: { type: 'string' },
      organizationId: { type: 'string' }
    },
    required: ['name']
  },

  // ===== RESPOSTAS RÁPIDAS (Quick Replies) =====
  QUICK_REPLY: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      text: { type: 'string' },
      departmentIds: { type: 'array', items: { type: 'string' } },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'text', 'departmentIds']
  },

  CREATE_QUICK_REPLY_REQUEST: {
    type: 'object',
    properties: {
      text: { type: 'string' },
      departmentIds: { type: 'array', items: { type: 'string' } }
    },
    required: ['text', 'departmentIds']
  },

  // ===== ROBÔS (Bots) =====
  BOT: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      type: { type: 'string', enum: ['whatsapp', 'telegram', 'email', 'sms'] },
      description: { type: 'string' },
      mainDepartmentId: { type: 'string' },
      departmentsIds: { type: 'array', items: { type: 'string' } },
      tagsIds: { type: 'array', items: { type: 'string' } },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'type', 'mainDepartmentId', 'departmentsIds', 'tagsIds']
  },

  CREATE_BOT_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      type: { type: 'string', enum: ['whatsapp', 'telegram', 'email', 'sms'] },
      description: { type: 'string' },
      mainDepartmentId: { type: 'string' },
      departmentsIds: { type: 'array', items: { type: 'string' } },
      tagsIds: { type: 'array', items: { type: 'string' } }
    },
    required: ['name', 'type', 'mainDepartmentId', 'departmentsIds', 'tagsIds']
  },

  // ===== TAGS =====
  TAG: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      color: { type: 'string' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'color']
  },

  CREATE_TAG_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      color: { type: 'string' }
    },
    required: ['name', 'color']
  },

  // ===== TICKETS =====
  TICKET: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      contactId: { type: 'string' },
      serviceId: { type: 'string' },
      departmentId: { type: 'string' },
      botId: { type: 'string' },
      subjectId: { type: 'string' },
      isRead: { type: 'boolean' },
      inTrash: { type: 'boolean' },
      status: { type: 'string', enum: ['open', 'closed', 'pending', 'in_progress', 'waiting', 'resolved'] },
      tagsIds: { type: 'array', items: { type: 'string' } },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'contactId', 'serviceId', 'departmentId', 'isRead', 'inTrash', 'status', 'tagsIds']
  },

  CREATE_TICKET_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      contactId: { type: 'string' },
      serviceId: { type: 'string' },
      departmentId: { type: 'string' },
      botId: { type: 'string' },
      isRead: { type: 'boolean' },
      inTrash: { type: 'boolean' },
      tagsIds: { type: 'array', items: { type: 'string' } }
    },
    required: ['name', 'contactId', 'serviceId', 'departmentId']
  },

  // ===== TOKENS =====
  TOKEN: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      token: { type: 'string' },
      lastUsedAt: { type: 'string', format: 'date-time' },
      createdAt: { type: 'string', format: 'date-time' },
      expiresAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'token']
  },

  // ===== USUÁRIOS (Users) =====
  USER: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      accountId: { type: 'string' },
      email: { type: 'string' },
      name: { type: 'string' },
      isAdmin: { type: 'boolean' },
      roles: { type: 'array', items: { $ref: '#/definitions/ROLE' } },
      organizationIds: { type: 'array', items: { type: 'string' } },
      departmentsId: { type: 'array', items: { type: 'string' } },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'accountId', 'email', 'name', 'isAdmin', 'organizationIds', 'departmentsId']
  },

  CREATE_USER_REQUEST: {
    type: 'object',
    properties: {
      accountId: { type: 'string' },
      email: { type: 'string' },
      password: { type: 'string' },
      name: { type: 'string' },
      rolesId: { type: 'string' },
      organizationIds: { type: 'array', items: { type: 'string' } },
      departmentsId: { type: 'array', items: { type: 'string' } }
    },
    required: ['accountId', 'email', 'password', 'name', 'departmentsId']
  },

  // ===== HORÁRIOS DE ATENDIMENTO (Attendance Schedules) =====
  ATTENDANCE_SCHEDULE: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      status: { type: 'string', enum: ['active', 'inactive'] },
      days: { type: 'array', items: { type: 'string' } },
      startTime: { type: 'string' },
      endTime: { type: 'string' },
      departmentIds: { type: 'array', items: { type: 'string' } },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'status', 'days', 'startTime', 'endTime', 'departmentIds']
  },

  CREATE_ATTENDANCE_SCHEDULE_REQUEST: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      status: { type: 'string', enum: ['active', 'inactive'] },
      days: { type: 'array', items: { type: 'string' } },
      startTime: { type: 'string' },
      endTime: { type: 'string' },
      departmentIds: { type: 'array', items: { type: 'string' } }
    },
    required: ['name', 'status', 'days', 'startTime', 'endTime', 'departmentIds']
  },

  // ===== MÉTRICAS (Metrics) =====
  METRIC: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      value: { type: 'number' },
      unit: { type: 'string' },
      timestamp: { type: 'string', format: 'date-time' },
      tags: { type: 'object' }
    },
    required: ['id', 'name', 'value', 'unit', 'timestamp']
  },

  // ===== EXPORTAÇÃO =====
  EXPORT_REQUEST: {
    type: 'object',
    properties: {
      type: { type: 'string', enum: ['comma', 'semiColon'] },
      format: { type: 'string', enum: ['csv', 'xlsx', 'json'] },
      fields: { type: 'array', items: { type: 'string' } },
      filters: { type: 'object' }
    },
    required: ['type']
  }
} as const;
