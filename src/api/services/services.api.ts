/**
 * API de Services (Conexões) da Digisac
 * Baseado na documentação oficial: digisac-api-doc.md
 */

import { ApiClient } from '../../utils/apiClient';
import { API_ENDPOINTS } from '../../config/apiEndpoints';
import { 
  Service, 
  CreateServiceRequest, 
  PaginatedResponse 
} from '../../types/api.types';

export class ServicesAPI {
  private apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  /**
   * Lista todos os serviços (conexões)
   */
  async getAllServices(): Promise<PaginatedResponse<Service>> {
    const response = await this.apiClient.get(API_ENDPOINTS.SERVICES.LIST);
    return response.body;
  }

  /**
   * Lista serviços com paginação
   */
  async getServicesWithPagination(page: number = 1, perPage: number = 40): Promise<PaginatedResponse<Service>> {
    const query = { page, perPage };
    const response = await this.apiClient.getWithQuery(API_ENDPOINTS.SERVICES.BASE, query);
    return response.body;
  }

  /**
   * Busca serviço por ID
   */
  async getServiceById(id: string): Promise<Service> {
    const response = await this.apiClient.get(API_ENDPOINTS.SERVICES.BY_ID(id));
    return response.body;
  }

  /**
   * Cria novo serviço
   */
  async createService(serviceData: CreateServiceRequest): Promise<Service> {
    const response = await this.apiClient.post(API_ENDPOINTS.SERVICES.BASE, serviceData);
    return response.body;
  }

  /**
   * Atualiza serviço existente
   */
  async updateService(id: string, serviceData: Partial<CreateServiceRequest>): Promise<Service> {
    const response = await this.apiClient.put(API_ENDPOINTS.SERVICES.BY_ID(id), serviceData);
    return response.body;
  }

  /**
   * Deleta serviço
   */
  async deleteService(id: string): Promise<void> {
    await this.apiClient.delete(API_ENDPOINTS.SERVICES.BY_ID(id));
  }

  /**
   * Reinicia serviço
   */
  async restartService(id: string): Promise<Service> {
    const response = await this.apiClient.post(API_ENDPOINTS.SERVICES.RESTART(id));
    return response.body;
  }

  /**
   * Gera QR Code para serviço (logout)
   */
  async generateQRCode(id: string): Promise<{ qrCode: string }> {
    const response = await this.apiClient.post(API_ENDPOINTS.SERVICES.LOGOUT(id));
    return response.body;
  }

  /**
   * Lista serviços por tipo
   */
  async getServicesByType(type: string): Promise<PaginatedResponse<Service>> {
    const query = { 'where[type]': type };
    const response = await this.apiClient.getWithQuery(API_ENDPOINTS.SERVICES.BASE, query);
    return response.body;
  }

  /**
   * Lista serviços por status
   */
  async getServicesByStatus(status: string): Promise<PaginatedResponse<Service>> {
    const query = { 'where[status]': status };
    const response = await this.apiClient.getWithQuery(API_ENDPOINTS.SERVICES.BASE, query);
    return response.body;
  }

  /**
   * Lista serviços com filtros avançados
   */
  async getServicesWithFilters(filters: Record<string, any>): Promise<PaginatedResponse<Service>> {
    const response = await this.apiClient.getWithQuery(API_ENDPOINTS.SERVICES.BASE, filters);
    return response.body;
  }

  /**
   * Busca serviços por nome
   */
  async searchServicesByName(name: string): Promise<PaginatedResponse<Service>> {
    const query = { 'where[name]': { $like: `%${name}%` } };
    const response = await this.apiClient.getWithQuery(API_ENDPOINTS.SERVICES.BASE, query);
    return response.body;
  }

  /**
   * Obtém estatísticas dos serviços
   */
  async getServicesStats(): Promise<{
    total: number;
    connected: number;
    disconnected: number;
    connecting: number;
    error: number;
  }> {
    const response = await this.apiClient.get(API_ENDPOINTS.SERVICES.LIST);
    const services = response.body.data;
    
    const stats = {
      total: response.body.total,
      connected: services.filter((s: any) => s.data?.status?.isConnected === true).length,
      disconnected: services.filter((s: any) => s.data?.status?.isConnected === false).length,
      connecting: services.filter((s: any) => s.data?.status?.isStarting === true).length,
      error: services.filter((s: any) => s.archivedAt !== null).length
    };

    return stats;
  }

  /**
   * Valida se serviço está funcionando
   */
  async validateService(id: string): Promise<boolean> {
    try {
      const service = await this.getServiceById(id);
      return service.data?.status?.isConnected === true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Obtém serviços ativos
   */
  async getActiveServices(): Promise<Service[]> {
    const response = await this.apiClient.get(API_ENDPOINTS.SERVICES.LIST);
    const services = response.body.data;
    return services.filter((s: any) => s.data?.status?.isConnected === true);
  }

  /**
   * Obtém serviços com erro
   */
  async getErrorServices(): Promise<Service[]> {
    const response = await this.apiClient.get(API_ENDPOINTS.SERVICES.LIST);
    const services = response.body.data;
    return services.filter((s: any) => s.archivedAt !== null);
  }

  /**
   * Obtém serviços por tipo específico
   */
  async getWhatsAppServices(): Promise<Service[]> {
    const response = await this.getServicesByType('whatsapp');
    return response.data;
  }

  async getTelegramServices(): Promise<Service[]> {
    const response = await this.getServicesByType('telegram');
    return response.data;
  }

  async getEmailServices(): Promise<Service[]> {
    const response = await this.getServicesByType('email');
    return response.data;
  }

  async getSMSServices(): Promise<Service[]> {
    const response = await this.getServicesByType('sms');
    return response.data;
  }

  /**
   * Obtém serviços criados em um período
   */
  async getServicesByDateRange(startDate: string, endDate: string): Promise<Service[]> {
    const query = {
      'where[createdAt]': {
        $gte: startDate,
        $lte: endDate
      }
    };
    const response = await this.getServicesWithFilters(query);
    return response.data;
  }

  /**
   * Obtém serviços ordenados por nome
   */
  async getServicesOrderedByName(order: 'ASC' | 'DESC' = 'ASC'): Promise<Service[]> {
    const query = {
      order: 'name',
      orderBy: order
    };
    const response = await this.getServicesWithFilters(query);
    return response.data;
  }

  /**
   * Obtém serviços ordenados por data de criação
   */
  async getServicesOrderedByCreatedAt(order: 'ASC' | 'DESC' = 'DESC'): Promise<Service[]> {
    const query = {
      order: 'createdAt',
      orderBy: order
    };
    const response = await this.getServicesWithFilters(query);
    return response.data;
  }

  /**
   * Obtém serviços com limite de atendimentos
   */
  async getServicesWithMaxCount(maxCount: number): Promise<Service[]> {
    const query = {
      'where[max_count]': {
        $lte: maxCount
      }
    };
    const response = await this.getServicesWithFilters(query);
    return response.data;
  }

  /**
   * Obtém serviços com webhook configurado
   */
  async getServicesWithWebhook(): Promise<Service[]> {
    const query = {
      'where[webHook]': {
        $ne: null
      }
    };
    const response = await this.getServicesWithFilters(query);
    return response.data;
  }

  /**
   * Obtém serviços sem webhook
   */
  async getServicesWithoutWebhook(): Promise<Service[]> {
    const query = {
      'where[webHook]': {
        $eq: null
      }
    };
    const response = await this.getServicesWithFilters(query);
    return response.data;
  }

  /**
   * Obtém informações detalhadas do serviço
   */
  async getServiceDetails(id: string): Promise<Service & {
    isActive: boolean;
    hasWebhook: boolean;
    daysSinceCreated: number;
  }> {
    const service = await this.getServiceById(id);
    const now = new Date();
    const created = new Date(service.createdAt);
    const daysSinceCreated = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));

    return {
      ...service,
      isActive: service.data?.status?.isConnected === true,
      hasWebhook: !!service.data?.webchat,
      daysSinceCreated
    };
  }
}
