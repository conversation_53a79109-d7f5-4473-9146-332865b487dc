import { ApiClient } from '../../utils/apiClient';
import { API_ENDPOINTS } from '../../config/apiEndpoints';
import {
  Department,
  CreateDepartmentRequest,
  PaginatedResponse
} from '../../types/api.types';

export class DepartmentsAPI {
  private apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getAllDepartments(): Promise<PaginatedResponse<Department>> {
    const response = await this.apiClient.get(API_ENDPOINTS.DEPARTMENTS.LIST);
    return response.body;
  }

  async getDepartmentsWithPagination(page: number = 1, perPage: number = 40): Promise<PaginatedResponse<Department>> {
    const query = { page, perPage } as Record<string, any>;
    const response = await this.apiClient.getWithQuery(API_ENDPOINTS.DEPARTMENTS.BASE, query);
    return response.body;
  }

  async getDepartmentById(id: string): Promise<Department> {
    const response = await this.apiClient.get(API_ENDPOINTS.DEPARTMENTS.BY_ID(id));
    return response.body;
  }

  async createDepartment(data: CreateDepartmentRequest): Promise<Department> {
    const response = await this.apiClient.post(API_ENDPOINTS.DEPARTMENTS.BASE, data);
    return response.body;
  }

  async updateDepartment(id: string, data: Partial<CreateDepartmentRequest>): Promise<Department> {
    const response = await this.apiClient.put(API_ENDPOINTS.DEPARTMENTS.BY_ID(id), data);
    return response.body;
  }

  async deleteDepartment(id: string): Promise<void> {
    await this.apiClient.delete(API_ENDPOINTS.DEPARTMENTS.BY_ID(id));
  }

  async getDepartmentUsers(id: string): Promise<PaginatedResponse<any>> {
    const response = await this.apiClient.get(API_ENDPOINTS.DEPARTMENTS.USERS(id));
    return response.body;
  }
}


