# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
*.tsbuildinfo

# Coverage reports
coverage/
*.lcov

# Test results
test-results/
junit.xml
test-results.xml

# Reports
reports/
allure-results/
allure-report/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Jest
jest-cache/

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Prettier
.prettiercache

# Lock files (keep package-lock.json)
yarn.lock
pnpm-lock.yaml

# Local development
.local/
local/

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Database
*.db
*.sqlite
*.sqlite3

# Certificates
*.pem
*.key
*.crt
*.p12

# Secrets
secrets/
*.secret
*.key
*.pem

# Test artifacts
test-artifacts/
screenshots/
videos/
traces/

# Performance test results
performance-results/
load-test-results/

# Benchmark results
benchmark-results/

# Profiling data
*.cpuprofile
*.heapprofile
*.heapsnapshot

# Memory dumps
*.dmp
*.hprof

# Core dumps
core.*

# Temporary test files
test-*.json
temp-*.json
*.test.json

# Generated files
generated/
auto-generated/

# Documentation build
docs/build/
docs/dist/

# Storybook
storybook-static/

# Chromatic
chromatic/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Cypress
cypress/videos/
cypress/screenshots/
cypress/downloads/

# Puppeteer
puppeteer-screenshots/

# Selenium
selenium-screenshots/

# WebDriver
webdriver-screenshots/

# Appium
appium-screenshots/

# Detox
detox-screenshots/

# Maestro
maestro-screenshots/

# Custom test artifacts
test-artifacts/
artifacts/

# Development
desenvolver.todo
