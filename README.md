# QA Automation Digisac - Jest + Supertest

Projeto de testes automatizados de API para a plataforma Digisac, desenvolvido com Jest + Supertest e TypeScript.

## Características

- ✅ Testes de API automatizados com Jest + Supertest
- ✅ Validação de schemas JSON com AJV
- ✅ Relatórios detalhados com cobertura de código
- ✅ Cache de autenticação OAuth 2.0
- ✅ Retry automático com backoff exponencial
- ✅ Geração de dados de teste com Faker.js
- ✅ Suporte a múltiplos ambientes
- ⚠️ Integração com CI/CD (em construção)
- ✅ 100% alinhado com a documentação oficial da API Digisac
- ✅ Padronizado com comportamento real da API Digisac (status 200 para todas operações de sucesso)

## Estrutura do Projeto

```
qa-automation-digisac-jest/
├── docs/                       # Documentações para contexto (de IA e manual)
├── src/
│   ├── api/                    # Classes de API organizadas por endpoint
│   │   └── services/           # API de Services (Conexões)
│   ├── utils/                  # Utilitários e helpers
│   │   ├── apiClient.ts        # Cliente HTTP com Supertest
│   │   ├── authHelper.ts       # Helper de autenticação OAuth 2.0
│   │   ├── responseValidator.ts # Validador de respostas
│   │   └── dataGenerator.ts    # Gerador de dados de teste
│   ├── config/                 # Configurações
│   │   ├── apiEndpoints.ts     # Endpoints da API Digisac
│   │   └── validationSchemas.ts # Schemas de validação JSON
│   └── types/                  # Definições TypeScript
│       ├── common.types.ts     # Tipos comuns
│       ├── api.types.ts        # Tipos da API Digisac
│       └── test.types.ts       # Tipos de teste
├── tests/                      # Testes Jest
│   ├── api/                    # Testes por endpoint
│   │   └── services.test.ts    # Testes da API de Services (exemplo)
│   ├── smoke/                  # Testes de smoke
│   │   └── connectivity.test.ts # Testes de conectividade (exemplo)
│   └── setup.ts                # Configuração global dos testes
├── coverage/                   # Relatórios de cobertura
├── reports/                    # Relatórios de teste
├── jest.config.js              # Configuração Jest
├── tsconfig.json               # Configuração TypeScript
├── package.json                # Dependências e scripts
└── README.md                   # Este arquivo
```

## Instalação

### Pré-requisitos

- Node.js 18+ 
- npm ou yarn
- Acesso à API Digisac

### Configuração do projeto

1. **Clone o repositório**
   ```bash
   git clone <repository-url>
   cd qa-automation-digisac-jest
   ```

2. **Instale as dependências**
   ```bash
   npm install
   ```

3. **Configure as variáveis de ambiente**
   ```bash
   cp env.example .env
   # Edite o arquivo .env com suas credenciais
   ```

## Executando os Testes

### Comandos Principais

```bash
# Executar todos os testes
npm test

# Executar testes em modo watch
npm run test:watch

# Executar testes com cobertura
npm run test:coverage

# Executar testes de smoke
npm run test:smoke

# Executar testes de API
npm run test:api

# Executar testes de performance
npm run test:performance

# Executar testes em modo CI
npm run test:ci
```

### Executar Testes Específicos

```bash
# Executar apenas testes de Services
npm test -- tests/api/services.test.ts

# Executar apenas testes de conectividade
npm test -- tests/smoke/connectivity.test.ts

# Executar testes com padrão específico
npm test -- --testNamePattern="deve listar"
```

## Relatórios

### Comandos para cobertura de Código

```bash
# Gerar relatório de cobertura
npm run test:coverage

# Abrir relatório no navegador
open coverage/lcov-report/index.html
```

### Relatórios JUnit

```bash
# Gerar relatório JUnit
npm run report:junit
```

## Arquivos de configuração

### Jest Configuration

O projeto está configurado com Jest para TypeScript e Supertest. A configuração está em `jest.config.js`:

- **Preset**: ts-jest
- **Environment**: node
- **Timeout**: 30 segundos
- **Coverage**: HTML, LCOV, JSON
- **Setup**: `tests/setup.ts`

### TypeScript Configuration

Configuração otimizada para Jest em `tsconfig.json`:

- **Target**: ES2020
- **Module**: CommonJS
- **Strict**: true
- **Paths**: Mapeamento de aliases

## Arquitetura

### Cliente HTTP (ApiClient)

```typescript
import { ApiClient } from './src/utils/apiClient';

const apiClient = new ApiClient('https://qa-automacao.digisac.chat');
apiClient.setAuthToken('your-token');

// GET
const response = await apiClient.get('/api/v1/services');

// POST
const response = await apiClient.post('/api/v1/services', data);

// PUT
const response = await apiClient.put('/api/v1/services/123', data);

// DELETE
await apiClient.delete('/api/v1/services/123');
```

### Autenticação (AuthHelper)

```typescript
import { AuthHelper } from './src/utils/authHelper';

const authHelper = new AuthHelper('https://qa-automacao.digisac.chat');

// Obter token
const token = await authHelper.getAuthenticatedToken();

// Obter headers autenticados
const headers = await authHelper.getAuthenticatedHeaders();

// Configurar autenticação no cliente
await authHelper.setupAuthentication(apiClient);
```

### Validação (ResponseValidator)

```typescript
import { ResponseValidator } from './src/utils/responseValidator';

const validator = new ResponseValidator();

// Validar status
validator.validateStatus(response, 200);

// Validar schema
const result = validator.validateResponse(response, schema);

// Validar campos obrigatórios
const result = validator.validateRequiredFields(response, ['id', 'name']);
```

### Geração de Dados (DataGenerator)

```typescript
import { DataGenerator } from './src/utils/dataGenerator';

// Gerar dados válidos
const serviceData = DataGenerator.generateServiceData();

// Gerar dados inválidos
const invalidData = DataGenerator.generateInvalidServiceData();

// Gerar dados de contato
const contactData = DataGenerator.generateContactData(serviceId);
```

## API Endpoints Suportados

### Services (Conexões)
- `GET /api/v1/services` - Listar serviços
- `GET /api/v1/services/{id}` - Buscar serviço
- `POST /api/v1/services` - Criar serviço
- `PUT /api/v1/services/{id}` - Atualizar serviço
- `DELETE /api/v1/services/{id}` - Deletar serviço
- `POST /api/v1/services/{id}/restart` - Reiniciar serviço
- `POST /api/v1/services/{id}/logout` - Gerar QR Code

### Outros Endpoints
- **Contatos**: `/api/v1/contacts`

### Departamentos
- `GET /api/v1/departments` - Listar departamentos
- `GET /api/v1/departments/{id}` - Buscar departamento
- `POST /api/v1/departments` - Criar departamento
- `PUT /api/v1/departments/{id}` - Editar departamento
- `POST /api/v1/departments/{id}/archive` - Arquivar departamento (quando disponível)
- `DELETE /api/v1/departments/{id}` - Excluir departamento

Exemplo de uso em testes:

```typescript
const apiClient = new ApiClient(process.env.API_BASE_URL);
const authHelper = new AuthHelper(process.env.API_BASE_URL);
const token = await authHelper.getAuthenticatedToken();
apiClient.setAuthToken(token);

const departments = new DepartmentsAPI(apiClient);
const list = await departments.getAllDepartments();
```
- **Usuários**: `/api/v1/users`
- **Tags**: `/api/v1/tags`
- **Tickets**: `/api/v1/tickets`
- **Mensagens**: `/api/v1/messages`
- **Campanhas**: `/api/v1/campaigns`
- **Bots**: `/api/v1/bots`
- E muitos outros...

## Padrão de Status HTTP da API Digisac

**⚠️ IMPORTANTE:** A API Digisac utiliza um padrão simplificado de status HTTP:

- **Todas as operações de sucesso retornam status 200** (POST, PUT, DELETE)
- **Não há diferenciação** entre criação (201), atualização (200) ou exclusão (204)
- **Este é o comportamento oficial** da API, conforme confirmado pelos desenvolvedores
- **Todos os testes devem usar `validator.validateStatus(response, 200)`** para operações de sucesso

### Exemplo Correto
```typescript
// ✅ CORRETO - Status 200 para todas operações de sucesso
validator.validateStatus(response, 200);

// ❌ INCORRETO - Não usar 201, 204, etc.
validator.validateStatus(response, 201); // Criação
validator.validateStatus(response, 204); // Exclusão
```

## Exemplos de Teste

### Teste Básico

```typescript
describe('API - Services', () => {
  let servicesAPI: ServicesAPI;

  beforeAll(async () => {
    const apiClient = new ApiClient(process.env.API_BASE_URL);
    await authHelper.setupAuthentication(apiClient);
    servicesAPI = new ServicesAPI(apiClient);
  });

  it('deve listar todos os serviços', async () => {
    const response = await servicesAPI.getAllServices();
    
    expect(response.data).toBeDefined();
    expect(Array.isArray(response.data)).toBe(true);
  });
});
```

### Teste com Validação

```typescript
it('deve criar serviço com dados válidos', async () => {
  const serviceData = DataGenerator.generateServiceData();
  const response = await servicesAPI.createService(serviceData);
  
  validator.validateStatus(response, 200);
  validator.validateResponse(response, VALIDATION_SCHEMAS.SERVICE);
  
  expect(response.body.name).toBe(serviceData.name);
});
```

### Teste de Performance

```typescript
it('deve responder em menos de 2 segundos', async () => {
  const startTime = Date.now();
  
  const response = await servicesAPI.getAllServices();
  
  const endTime = Date.now();
  const responseTime = endTime - startTime;
  
  expect(responseTime).toBeLessThan(2000);
});
```

## CI/CD

### GitHub Actions

```yaml
name: API Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm run test:ci
      env:
        API_BASE_URL: ${{ secrets.API_BASE_URL }}
        API_USERNAME: ${{ secrets.API_USERNAME }}
        API_PASSWORD: ${{ secrets.API_PASSWORD }}
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
```

## Métricas e Monitoramento

### Cobertura de Código
- **Meta**: > 80%
- **Relatórios**: HTML, LCOV, JSON
- **Exclusões**: Arquivos de teste, tipos

### Performance
- **Timeout**: 30 segundos
- **Retry**: 3 tentativas
- **Threshold**: < 2 segundos

### Qualidade
- **ESLint**: Configurado
- **TypeScript**: Strict mode
- **Validação**: Schemas JSON

## Contribuição

### Como contribuir

1. Fork o projeto
2. Crie uma branch a partir da develop para sua feature (`git checkout -b feature/nova-feature`)
3. Commit suas mudanças (`git commit -m 'Adiciona nova feature'`)
4. Push para a branch (`git push origin feature/nova-feature`)
5. Abra uma MR de volta para a develop

### Regras ao contribuir

1. Nunca suba alterações diretamente na develop. Sempre crie sua própria branch (a partir da develop) e depois peça o merge de volta (também para a develop);
2. A branch main só deve receber MR da develop, e somente quando a develop for considerada segura para mergear;
3. A main deve sempre estar disponível para ser utilizada por todos (funcionando), então deve ser tratada similarmente a uma versão de produção, enquanto a develop deve ser tratada similarmente a uma RC;
4. Sempre ao criar MR da sua branch para Develop, marque a opção "Delete source branch when merge request is accepted", em Merge options. Assim, a sua branch antiga será automaticamente excluída do repositório logo após ser mesclada com a develop, reduzindo o acúmulo de branchs desnecessárias;
5. JAMAIS marque a opção "Delete source branch when merge request is accepted" em Merge options, quando for realizar o merge da develop para a main. Essas duas branchs devem permanecer sempre ativas, e nunca devem ser excluídas. A main já é protegida, mas a develop deve ser tratada com cautela. 

## Licença

Este projeto está sob o domínio da Ikatec

---
