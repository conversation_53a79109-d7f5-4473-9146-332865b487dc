# Instruções para Criação do Projeto qa-automation-digisac-api

## Visão Geral

Este documento contém instruções completas para criar um novo projeto de testes de API para a plataforma Digisac, utilizando Playwright com TypeScript. O projeto será focado exclusivamente em testes de API, seguindo as melhores práticas e padrões estabelecidos no projeto de testes E2E existente.

## Estrutura do Novo Projeto

### Nome do Projeto
```
qa-automation-digisac-api
```

### Estrutura de Diretórios Recomendada
```
qa-automation-digisac-api/
├── src/
│   ├── api/
│   │   ├── auth/
│   │   │   ├── auth.api.ts
│   │   │   ├── auth.spec.ts
│   │   │   └── auth.types.ts
│   │   ├── connections/
│   │   │   ├── connections.api.ts
│   │   │   ├── connections.spec.ts
│   │   │   └── connections.types.ts
│   │   ├── people/
│   │   │   ├── people.api.ts
│   │   │   ├── people.spec.ts
│   │   │   └── people.types.ts
│   │   ├── organizations/
│   │   │   ├── organizations.api.ts
│   │   │   ├── organizations.spec.ts
│   │   │   └── organizations.types.ts
│   │   ├── campaigns/
│   │   │   ├── campaigns.api.ts
│   │   │   ├── campaigns.spec.ts
│   │   │   └── campaigns.types.ts
│   │   ├── tags/
│   │   │   ├── tags.api.ts
│   │   │   ├── tags.spec.ts
│   │   │   └── tags.types.ts
│   │   ├── notifications/
│   │   │   ├── notifications.api.ts
│   │   │   ├── notifications.spec.ts
│   │   │   └── notifications.types.ts
│   │   └── integrations/
│   │       ├── integrations.api.ts
│   │       ├── integrations.spec.ts
│   │       └── integrations.types.ts
│   ├── utils/
│   │   ├── apiHelper.ts
│   │   ├── authHelper.ts
│   │   ├── dataGenerator.ts
│   │   ├── env.ts
│   │   ├── responseValidator.ts
│   │   ├── testData.ts
│   │   └── tokenCache.ts
│   ├── config/
│   │   ├── apiEndpoints.ts
│   │   ├── testConfig.ts
│   │   └── validationSchemas.ts
│   └── types/
│       ├── common.types.ts
│       ├── api.types.ts
│       └── test.types.ts
├── tests/
│   ├── fixtures/
│   │   └── testData.json
│   └── schemas/
│       └── validation/
├── reports/
│   ├── allure-results/
│   └── html-report/
├── docs/
│   ├── API_DOCUMENTATION.md
│   ├── TEST_STRATEGY.md
│   └── SETUP_GUIDE.md
├── scripts/
│   ├── setup.sh
│   ├── run-tests.sh
│   └── generate-report.sh
├── .env.example
├── .gitignore
├── package.json
├── playwright.config.ts
├── tsconfig.json
├── README.md
└── docker-compose.yml
```

## Configuração Inicial

### 1. Criação do Projeto

```bash
# Criar diretório do projeto
mkdir qa-automation-digisac-api
cd qa-automation-digisac-api

# Inicializar projeto Node.js
npm init -y

# Instalar dependências principais
npm install --save-dev @playwright/test typescript @types/node
npm install --save-dev allure-playwright allure-commandline
npm install --save-dev @typescript-eslint/eslint-plugin @typescript-eslint/parser
npm install --save-dev eslint eslint-plugin-playwright
npm install --save-dev dotenv

# Instalar dependências de produção
npm install @faker-js/faker
npm install ajv # Para validação de schemas JSON
npm install joi # Alternativa para validação
npm install supertest # Para testes de API (alternativa ao Playwright)
```

### 2. Configuração do TypeScript (tsconfig.json)

```json
{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "lib": ["ESNext"],
    "moduleResolution": "Node",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    "resolveJsonModule": true,
    "outDir": "dist",
    "baseUrl": ".",
    "allowSyntheticDefaultImports": true,
    "declaration": true,
    "sourceMap": true,
    "rootDir": "src",
    "paths": {
      "@/*": ["src/*"],
      "@utils/*": ["src/utils/*"],
      "@config/*": ["src/config/*"],
      "@types/*": ["src/types/*"]
    }
  },
  "include": ["src/**/*", "tests/**/*"],
  "exclude": ["node_modules", "dist", "reports"]
}
```

### 3. Configuração do Playwright (playwright.config.ts)

```typescript
import { defineConfig, devices } from '@playwright/test';
import dotenv from 'dotenv';

dotenv.config();

export default defineConfig({
  testDir: './src',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html', { outputFolder: 'reports/html-report' }],
    ['allure-playwright', {
      detail: true,
      outputFolder: 'reports/allure-results',
      suiteTitle: false
    }],
    ['json', { outputFile: 'reports/test-results.json' }],
    ['junit', { outputFile: 'reports/junit.xml' }]
  ],
  use: {
    baseURL: process.env.API_BASE_URL || 'https://qa.digisac.chat/api/v1',
    extraHTTPHeaders: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    actionTimeout: 10000,
    navigationTimeout: 10000,
    trace: 'retain-on-failure',
  },
  projects: [
    {
      name: 'api-tests',
      testMatch: /.*\.spec\.ts/,
    },
    {
      name: 'smoke-tests',
      testMatch: /.*\.smoke\.spec\.ts/,
    },
    {
      name: 'performance-tests',
      testMatch: /.*\.perf\.spec\.ts/,
    }
  ],
  globalSetup: require.resolve('./src/utils/globalSetup.ts'),
  globalTeardown: require.resolve('./src/utils/globalTeardown.ts'),
});
```

### 4. Configuração de Variáveis de Ambiente (.env.example)

```bash
# Configurações da API
API_BASE_URL=https://qa.digisac.chat/api/v1
API_VERSION=v1

# Credenciais de autenticação
API_CLIENT_ID=api
API_CLIENT_SECRET=secret
API_USERNAME=<EMAIL>
API_PASSWORD=sua_senha

# Configurações de teste
TEST_ENVIRONMENT=qa
TEST_TIMEOUT=30000
MAX_RETRIES=3
RETRY_DELAY=1000

# Configurações de relatório
ALLURE_RESULTS_DIR=reports/allure-results
HTML_REPORT_DIR=reports/html-report

# Configurações de integração
INTEGRATION_WEBHOOK_URL=https://hooks.slack.com/services/...
SLACK_CHANNEL=#qa-automation

# Configurações de performance
PERFORMANCE_THRESHOLD_RESPONSE_TIME=2000
PERFORMANCE_THRESHOLD_THROUGHPUT=100
```

## Estrutura de Arquivos Principais

### 1. Utilitários Base (src/utils/)

#### apiHelper.ts
```typescript
import { APIRequestContext, APIResponse } from '@playwright/test';
import { requestWithRetry, throttleRequest } from './requestHelper';

export class ApiHelper {
  private requestContext: APIRequestContext;
  private baseURL: string;

  constructor(requestContext: APIRequestContext, baseURL: string) {
    this.requestContext = requestContext;
    this.baseURL = baseURL;
  }

  async get(endpoint: string, headers?: Record<string, string>): Promise<APIResponse> {
    return this.request('GET', endpoint, { headers });
  }

  async post(endpoint: string, data?: any, headers?: Record<string, string>): Promise<APIResponse> {
    return this.request('POST', endpoint, { data, headers });
  }

  async put(endpoint: string, data?: any, headers?: Record<string, string>): Promise<APIResponse> {
    return this.request('PUT', endpoint, { data, headers });
  }

  async delete(endpoint: string, headers?: Record<string, string>): Promise<APIResponse> {
    return this.request('DELETE', endpoint, { headers });
  }

  async patch(endpoint: string, data?: any, headers?: Record<string, string>): Promise<APIResponse> {
    return this.request('PATCH', endpoint, { data, headers });
  }

  private async request(
    method: string,
    endpoint: string,
    options: { data?: any; headers?: Record<string, string> } = {}
  ): Promise<APIResponse> {
    const url = `${this.baseURL}${endpoint}`;
    const requestOptions = {
      data: options.data,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    return requestWithRetry(
      () => this.requestContext[method.toLowerCase()](url, requestOptions),
      3,
      1000
    );
  }
}
```

#### authHelper.ts
```typescript
import { APIRequestContext } from '@playwright/test';
import { getAuthToken, clearTokenCache } from './tokenCache';

export class AuthHelper {
  private requestContext: APIRequestContext;
  private baseURL: string;

  constructor(requestContext: APIRequestContext, baseURL: string) {
    this.requestContext = requestContext;
    this.baseURL = baseURL;
  }

  async authenticate(): Promise<string> {
    const tokenData = await getAuthToken(this.requestContext);
    return tokenData.access_token;
  }

  async getAuthenticatedHeaders(): Promise<Record<string, string>> {
    const token = await this.authenticate();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  async logout(): Promise<void> {
    clearTokenCache();
  }

  async refreshToken(): Promise<string> {
    clearTokenCache();
    return await this.authenticate();
  }
}
```

#### responseValidator.ts
```typescript
import { APIResponse } from '@playwright/test';
import Ajv from 'ajv';

export class ResponseValidator {
  private ajv: Ajv;

  constructor() {
    this.ajv = new Ajv({ allErrors: true });
  }

  async validateResponse(response: APIResponse, schema: any): Promise<boolean> {
    const responseData = await response.json();
    const validate = this.ajv.compile(schema);
    const isValid = validate(responseData);

    if (!isValid) {
      console.error('Validation errors:', validate.errors);
      throw new Error(`Response validation failed: ${JSON.stringify(validate.errors)}`);
    }

    return true;
  }

  async validateStatus(response: APIResponse, expectedStatus: number): Promise<void> {
    if (response.status() !== expectedStatus) {
      const responseText = await response.text();
      throw new Error(
        `Expected status ${expectedStatus}, but got ${response.status()}. Response: ${responseText}`
      );
    }
  }

  async validateResponseTime(response: APIResponse, maxTime: number): Promise<void> {
    const responseTime = response.request().timing().responseEnd - response.request().timing().requestStart;
    if (responseTime > maxTime) {
      throw new Error(`Response time ${responseTime}ms exceeds maximum allowed time ${maxTime}ms`);
    }
  }
}
```

### 2. Configurações (src/config/)

#### apiEndpoints.ts
```typescript
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/oauth/token',
    REFRESH: '/oauth/token/refresh',
    LOGOUT: '/oauth/logout',
  },
  CONNECTIONS: {
    BASE: '/connections',
    BY_ID: (id: string) => `/connections/${id}`,
    SEARCH: '/connections/search',
    STATUS: (id: string) => `/connections/${id}/status`,
  },
  PEOPLE: {
    BASE: '/people',
    BY_ID: (id: string) => `/people/${id}`,
    SEARCH: '/people/search',
    CREATE: '/people',
    UPDATE: (id: string) => `/people/${id}`,
    DELETE: (id: string) => `/people/${id}`,
  },
  ORGANIZATIONS: {
    BASE: '/organizations',
    BY_ID: (id: string) => `/organizations/${id}`,
    SEARCH: '/organizations/search',
    CREATE: '/organizations',
    UPDATE: (id: string) => `/organizations/${id}`,
    DELETE: (id: string) => `/organizations/${id}`,
  },
  CAMPAIGNS: {
    BASE: '/campaigns',
    BY_ID: (id: string) => `/campaigns/${id}`,
    SEARCH: '/campaigns/search',
    CREATE: '/campaigns',
    UPDATE: (id: string) => `/campaigns/${id}`,
    DELETE: (id: string) => `/campaigns/${id}`,
  },
  TAGS: {
    BASE: '/tags',
    BY_ID: (id: string) => `/tags/${id}`,
    SEARCH: '/tags/search',
    CREATE: '/tags',
    UPDATE: (id: string) => `/tags/${id}`,
    DELETE: (id: string) => `/tags/${id}`,
  },
  NOTIFICATIONS: {
    BASE: '/notifications',
    BY_ID: (id: string) => `/notifications/${id}`,
    SEARCH: '/notifications/search',
    MARK_READ: (id: string) => `/notifications/${id}/read`,
    MARK_UNREAD: (id: string) => `/notifications/${id}/unread`,
  },
  INTEGRATIONS: {
    BASE: '/integrations',
    BY_ID: (id: string) => `/integrations/${id}`,
    SEARCH: '/integrations/search',
    CREATE: '/integrations',
    UPDATE: (id: string) => `/integrations/${id}`,
    DELETE: (id: string) => `/integrations/${id}`,
    TEST: (id: string) => `/integrations/${id}/test`,
  },
} as const;
```

#### validationSchemas.ts
```typescript
export const VALIDATION_SCHEMAS = {
  ERROR_RESPONSE: {
    type: 'object',
    properties: {
      error: { type: 'string' },
      message: { type: 'string' },
      statusCode: { type: 'number' },
      timestamp: { type: 'string' },
    },
    required: ['error', 'message', 'statusCode', 'timestamp'],
  },
  
  SUCCESS_RESPONSE: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      data: { type: 'object' },
    },
    required: ['success', 'data'],
  },

  CONNECTION: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      status: { type: 'string' },
      provider: { type: 'string' },
      createdAt: { type: 'string' },
      updatedAt: { type: 'string' },
    },
    required: ['id', 'name', 'status', 'provider'],
  },

  PERSON: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      email: { type: 'string' },
      phone: { type: 'string' },
      organizationId: { type: 'string' },
      createdAt: { type: 'string' },
      updatedAt: { type: 'string' },
    },
    required: ['id', 'name', 'email'],
  },

  ORGANIZATION: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      domain: { type: 'string' },
      status: { type: 'string' },
      createdAt: { type: 'string' },
      updatedAt: { type: 'string' },
    },
    required: ['id', 'name', 'status'],
  },
} as const;
```

### 3. Exemplo de Teste de API (src/api/connections/connections.spec.ts)

```typescript
import { test, expect } from '@playwright/test';
import { ConnectionsAPI } from './connections.api';
import { ResponseValidator } from '../../utils/responseValidator';
import { VALIDATION_SCHEMAS } from '../../config/validationSchemas';
import { generateConnectionData } from '../../utils/dataGenerator';

test.describe('API - Connections', () => {
  let connectionsAPI: ConnectionsAPI;
  let validator: ResponseValidator;

  test.beforeEach(async ({ request }) => {
    const baseURL = process.env.API_BASE_URL || 'https://qa.digisac.chat/api/v1';
    connectionsAPI = new ConnectionsAPI(request, baseURL);
    validator = new ResponseValidator();
  });

  test.describe('GET /connections', () => {
    test('Deve retornar lista de conexões com sucesso', async () => {
      const response = await connectionsAPI.getAllConnections();
      
      await validator.validateStatus(response, 200);
      await validator.validateResponse(response, {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: VALIDATION_SCHEMAS.CONNECTION,
          },
          total: { type: 'number' },
          page: { type: 'number' },
          perPage: { type: 'number' },
        },
        required: ['data', 'total', 'page', 'perPage'],
      });
    });

    test('Deve retornar conexões paginadas corretamente', async () => {
      const page = 1;
      const perPage = 10;
      
      const response = await connectionsAPI.getConnectionsWithPagination(page, perPage);
      
      await validator.validateStatus(response, 200);
      const responseData = await response.json();
      
      expect(responseData.page).toBe(page);
      expect(responseData.perPage).toBe(perPage);
      expect(responseData.data.length).toBeLessThanOrEqual(perPage);
    });

    test('Deve filtrar conexões por status', async () => {
      const status = 'active';
      
      const response = await connectionsAPI.getConnectionsByStatus(status);
      
      await validator.validateStatus(response, 200);
      const responseData = await response.json();
      
      // Verificar se todas as conexões retornadas têm o status especificado
      responseData.data.forEach((connection: any) => {
        expect(connection.status).toBe(status);
      });
    });
  });

  test.describe('GET /connections/{id}', () => {
    test('Deve retornar conexão específica por ID', async () => {
      // Primeiro, obter uma lista de conexões para pegar um ID válido
      const listResponse = await connectionsAPI.getAllConnections();
      const listData = await listResponse.json();
      
      if (listData.data.length === 0) {
        test.skip('Nenhuma conexão disponível para teste');
        return;
      }
      
      const connectionId = listData.data[0].id;
      const response = await connectionsAPI.getConnectionById(connectionId);
      
      await validator.validateStatus(response, 200);
      await validator.validateResponse(response, VALIDATION_SCHEMAS.CONNECTION);
    });

    test('Deve retornar 404 para ID inexistente', async () => {
      const invalidId = '00000000-0000-0000-0000-000000000000';
      
      const response = await connectionsAPI.getConnectionById(invalidId);
      
      await validator.validateStatus(response, 404);
      await validator.validateResponse(response, VALIDATION_SCHEMAS.ERROR_RESPONSE);
    });
  });

  test.describe('POST /connections', () => {
    test('Deve criar nova conexão com dados válidos', async () => {
      const connectionData = generateConnectionData();
      
      const response = await connectionsAPI.createConnection(connectionData);
      
      await validator.validateStatus(response, 201);
      await validator.validateResponse(response, VALIDATION_SCHEMAS.CONNECTION);
      
      const responseData = await response.json();
      expect(responseData.name).toBe(connectionData.name);
      expect(responseData.provider).toBe(connectionData.provider);
    });

    test('Deve retornar erro 400 para dados inválidos', async () => {
      const invalidData = {
        name: '', // Nome vazio
        provider: 'invalid_provider',
      };
      
      const response = await connectionsAPI.createConnection(invalidData);
      
      await validator.validateStatus(response, 400);
      await validator.validateResponse(response, VALIDATION_SCHEMAS.ERROR_RESPONSE);
    });
  });

  test.describe('PUT /connections/{id}', () => {
    test('Deve atualizar conexão existente', async () => {
      // Primeiro, criar uma conexão para depois atualizar
      const connectionData = generateConnectionData();
      const createResponse = await connectionsAPI.createConnection(connectionData);
      const createdConnection = await createResponse.json();
      
      const updateData = {
        name: 'Updated Connection Name',
        status: 'inactive',
      };
      
      const response = await connectionsAPI.updateConnection(createdConnection.id, updateData);
      
      await validator.validateStatus(response, 200);
      await validator.validateResponse(response, VALIDATION_SCHEMAS.CONNECTION);
      
      const responseData = await response.json();
      expect(responseData.name).toBe(updateData.name);
      expect(responseData.status).toBe(updateData.status);
    });
  });

  test.describe('DELETE /connections/{id}', () => {
    test('Deve deletar conexão existente', async () => {
      // Primeiro, criar uma conexão para depois deletar
      const connectionData = generateConnectionData();
      const createResponse = await connectionsAPI.createConnection(connectionData);
      const createdConnection = await createResponse.json();
      
      const response = await connectionsAPI.deleteConnection(createdConnection.id);
      
      await validator.validateStatus(response, 204);
      
      // Verificar se a conexão foi realmente deletada
      const getResponse = await connectionsAPI.getConnectionById(createdConnection.id);
      expect(getResponse.status()).toBe(404);
    });
  });

  test.describe('Performance Tests', () => {
    test('Deve responder em menos de 2 segundos', async () => {
      const startTime = Date.now();
      
      const response = await connectionsAPI.getAllConnections();
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      await validator.validateStatus(response, 200);
      expect(responseTime).toBeLessThan(2000);
    });

    test('Deve suportar múltiplas requisições simultâneas', async () => {
      const promises = Array.from({ length: 10 }, () => 
        connectionsAPI.getAllConnections()
      );
      
      const responses = await Promise.all(promises);
      
      responses.forEach(response => {
        expect(response.status()).toBe(200);
      });
    });
  });
});
```

## Scripts de Automação

### 1. Script de Setup (scripts/setup.sh)

```bash
#!/bin/bash

echo "🚀 Configurando projeto qa-automation-digisac-api..."

# Verificar se o Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Node.js não encontrado. Instale o Node.js primeiro."
    exit 1
fi

# Verificar se o npm está instalado
if ! command -v npm &> /dev/null; then
    echo "❌ npm não encontrado. Instale o npm primeiro."
    exit 1
fi

# Instalar dependências
echo "📦 Instalando dependências..."
npm install

# Instalar Playwright
echo "🎭 Instalando Playwright..."
npx playwright install

# Criar diretórios necessários
echo "📁 Criando estrutura de diretórios..."
mkdir -p src/{api,utils,config,types}
mkdir -p src/api/{auth,connections,people,organizations,campaigns,tags,notifications,integrations}
mkdir -p tests/{fixtures,schemas/validation}
mkdir -p reports/{allure-results,html-report}
mkdir -p docs
mkdir -p scripts

# Copiar arquivos de configuração
echo "⚙️ Configurando arquivos..."
cp .env.example .env

echo "✅ Configuração concluída!"
echo "📝 Edite o arquivo .env com suas credenciais"
echo "🚀 Execute 'npm test' para rodar os testes"
```

### 2. Script de Execução de Testes (scripts/run-tests.sh)

```bash
#!/bin/bash

# Configurações
TEST_TYPE=${1:-all}
ENVIRONMENT=${2:-qa}
PARALLEL=${3:-true}

echo "🧪 Executando testes de API..."
echo "Tipo: $TEST_TYPE"
echo "Ambiente: $ENVIRONMENT"
echo "Paralelo: $PARALLEL"

# Definir variáveis de ambiente
export TEST_ENVIRONMENT=$ENVIRONMENT

case $TEST_TYPE in
    "smoke")
        echo "🔥 Executando testes de smoke..."
        npx playwright test --grep "smoke" --project=smoke-tests
        ;;
    "performance")
        echo "⚡ Executando testes de performance..."
        npx playwright test --grep "performance" --project=performance-tests
        ;;
    "api")
        echo "🔌 Executando todos os testes de API..."
        npx playwright test --project=api-tests
        ;;
    "all")
        echo "🎯 Executando todos os testes..."
        npx playwright test
        ;;
    *)
        echo "❌ Tipo de teste inválido: $TEST_TYPE"
        echo "Tipos válidos: smoke, performance, api, all"
        exit 1
        ;;
esac

# Gerar relatório
echo "📊 Gerando relatório..."
npm run allure:report

echo "✅ Execução concluída!"
```

## Documentação

### 1. README.md Principal

```markdown
# QA Automation Digisac API

## Descrição

Projeto de testes automatizados de API para a plataforma Digisac, desenvolvido com Playwright e TypeScript. Focado em testes de funcionalidade, performance e validação de contratos de API.

## Características

- ✅ Testes de API automatizados com Playwright
- ✅ Validação de schemas JSON
- ✅ Testes de performance e carga
- ✅ Relatórios detalhados com Allure
- ✅ Cache de autenticação
- ✅ Retry automático com backoff exponencial
- ✅ Geração de dados de teste com Faker
- ✅ Suporte a múltiplos ambientes
- ✅ Integração com CI/CD

## Estrutura do Projeto

```
src/
├── api/           # Testes organizados por endpoint
├── utils/         # Utilitários e helpers
├── config/        # Configurações e schemas
└── types/         # Definições de tipos TypeScript
```

## Instalação

```bash
# Clone o repositório
git clone <repository-url>
cd qa-automation-digisac-api

# Execute o script de setup
chmod +x scripts/setup.sh
./scripts/setup.sh

# Configure as variáveis de ambiente
cp .env.example .env
# Edite o arquivo .env com suas credenciais
```

## Executando os Testes

```bash
# Todos os testes
npm test

# Testes específicos
npm run test:smoke
npm run test:performance
npm run test:api

# Com relatório
npm run test:report
```

## Relatórios

- **HTML**: `npm run report:html`
- **Allure**: `npm run report:allure`
- **JUnit**: `npm run report:junit`

## Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## Licença

Este projeto está sob a licença MIT.
```

### 2. Documentação de API (docs/API_DOCUMENTATION.md)

```markdown
# Documentação da API Digisac

## Visão Geral

Esta documentação descreve os endpoints da API Digisac e como testá-los.

## Autenticação

### OAuth 2.0
- **Endpoint**: `/oauth/token`
- **Método**: POST
- **Grant Type**: Password
- **Headers**: `Content-Type: application/x-www-form-urlencoded`

### Exemplo de Request
```bash
curl -X POST "https://qa.digisac.chat/api/v1/oauth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&username=<EMAIL>&password=senha&client_id=api&client_secret=secret"
```

## Endpoints

### Connections
- `GET /connections` - Listar conexões
- `GET /connections/{id}` - Obter conexão específica
- `POST /connections` - Criar nova conexão
- `PUT /connections/{id}` - Atualizar conexão
- `DELETE /connections/{id}` - Deletar conexão

### People
- `GET /people` - Listar pessoas
- `GET /people/{id}` - Obter pessoa específica
- `POST /people` - Criar nova pessoa
- `PUT /people/{id}` - Atualizar pessoa
- `DELETE /people/{id}` - Deletar pessoa

### Organizations
- `GET /organizations` - Listar organizações
- `GET /organizations/{id}` - Obter organização específica
- `POST /organizations` - Criar nova organização
- `PUT /organizations/{id}` - Atualizar organização
- `DELETE /organizations/{id}` - Deletar organização

## Códigos de Status

- `200` - Sucesso
- `201` - Criado
- `204` - Sem conteúdo
- `400` - Bad Request
- `401` - Não autorizado
- `403` - Proibido
- `404` - Não encontrado
- `429` - Too Many Requests
- `500` - Erro interno do servidor

## Rate Limiting

- **Limite**: 1000 requests por hora por usuário
- **Headers de resposta**:
  - `X-RateLimit-Limit`: Limite total
  - `X-RateLimit-Remaining`: Requests restantes
  - `X-RateLimit-Reset`: Timestamp de reset

## Paginação

Todos os endpoints de listagem suportam paginação:

- `page`: Número da página (padrão: 1)
- `perPage`: Itens por página (padrão: 20, máximo: 100)

## Filtros

Endpoints de listagem suportam filtros:

- `status`: Filtrar por status
- `createdAt`: Filtrar por data de criação
- `updatedAt`: Filtrar por data de atualização

## Exemplos de Uso

### Listar conexões com filtros
```bash
GET /connections?status=active&page=1&perPage=10
```

### Criar pessoa
```json
POST /people
{
  "name": "João Silva",
  "email": "<EMAIL>",
  "phone": "+5511999999999",
  "organizationId": "uuid-da-organizacao"
}
```

## Schemas de Validação

Todos os endpoints incluem validação de schema. Consulte `src/config/validationSchemas.ts` para os schemas completos.
```

## Scripts de CI/CD

### 1. GitHub Actions (.github/workflows/api-tests.yml)

```yaml
name: API Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  api-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright
      run: npx playwright install --with-deps
    
    - name: Run API tests
      run: npm test
      env:
        API_BASE_URL: ${{ secrets.API_BASE_URL }}
        API_USERNAME: ${{ secrets.API_USERNAME }}
        API_PASSWORD: ${{ secrets.API_PASSWORD }}
    
    - name: Generate Allure Report
      run: npm run allure:generate
    
    - name: Upload Allure Report
      uses: actions/upload-artifact@v4
      with:
        name: allure-report
        path: allure-report/
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      with:
        name: test-results
        path: test-results/
```

### 2. Docker Compose (docker-compose.yml)

```yaml
version: '3.8'

services:
  qa-automation-api:
    build: .
    environment:
      - NODE_ENV=test
      - API_BASE_URL=${API_BASE_URL}
      - API_USERNAME=${API_USERNAME}
      - API_PASSWORD=${API_PASSWORD}
    volumes:
      - ./reports:/app/reports
      - ./src:/app/src
    command: npm test

  allure-server:
    image: frankescobar/allure-docker-service
    ports:
      - "5050:5050"
    volumes:
      - ./reports/allure-results:/app/allure-results
      - ./reports/allure-report:/app/default-reports
    environment:
      CHECK_RESULTS_EVERY_SECONDS: 1
      KEEP_HISTORY: 1
      OPTIONS: "--report.port 5050"
```

## Melhorias e Recursos Avançados

### 1. Testes de Performance

- **Thresholds configuráveis** para tempo de resposta
- **Testes de carga** com múltiplas requisições simultâneas
- **Métricas de performance** integradas aos relatórios

### 2. Validação Avançada

- **Schemas JSON** para validação de contratos
- **Validação de tipos** com TypeScript
- **Custom validators** para regras de negócio específicas

### 3. Gerenciamento de Dados

- **Faker.js** para geração de dados de teste
- **Cleanup automático** de dados de teste
- **Fixtures reutilizáveis** para cenários comuns

### 4. Monitoramento e Observabilidade

- **Logs estruturados** com diferentes níveis
- **Métricas de teste** (duração, sucesso, falha)
- **Integração com ferramentas** de monitoramento

## Conclusão

Este projeto fornece uma base sólida para testes automatizados de API da plataforma Digisac, seguindo as melhores práticas de automação de testes e mantendo a consistência com o projeto de testes E2E existente. A estrutura modular e as configurações flexíveis permitem fácil manutenção e extensão dos testes conforme a API evolui.
