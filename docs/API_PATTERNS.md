# Padrões da API Digisac

Este documento contém informações importantes sobre os padrões e comportamentos específicos da API Digisac, conforme confirmado pelos desenvolvedores.

## Status HTTP

### Padrão Simplificado
A API Digisac utiliza um padrão simplificado de status HTTP:

- **Todas as operações de sucesso retornam status 200**
- **Não há diferenciação** entre:
  - Criação (POST) - retorna 200, não 201
  - Atualização (PUT) - retorna 200
  - Exclusão (DELETE) - retorna 200, não 204
- **Este é o comportamento oficial** da API
- **Não há questões de segurança** envolvidas, é apenas simplificação

### Implementação nos Testes

```typescript
// ✅ CORRETO - Sempre usar 200 para operações de sucesso
validator.validateStatus(response, 200);

// ❌ INCORRETO - Não usar códigos específicos
validator.validateStatus(response, 201); // Criação
validator.validateStatus(response, 204); // Exclusão
```

### Códigos de Erro
Para operações que falham, a API retorna os códigos apropriados:
- **400** - Bad Request (dados inválidos)
- **401** - Unauthorized (token inválido)
- **402** - Payment Required (limite do plano atingido)
- **403** - Forbidden (sem permissão)
- **404** - Not Found (recurso não encontrado)
- **422** - Unprocessable Entity (validação falhou)

## Estrutura de Resposta

### Respostas Paginadas
```typescript
{
  data: T[],
  total: number,
  limit: number,
  skip: number,
  currentPage: number,
  lastPage: number,
  from: number,
  to: number
}
```

### Respostas de Erro
```typescript
{
  error: string,
  message: string,
  statusCode: number,
  timestamp: string,
  path: string
}
```

## Autenticação

- **Tipo:** OAuth 2.0
- **Token:** Hash format (hex), não JWT
- **Header:** `Authorization: Bearer {token}`
- **Cache:** Tokens são cacheados para evitar requisições desnecessárias

## Validação

- **Schemas:** JSON Schema com AJV
- **Campos obrigatórios:** Sempre validar com `validator.validateRequiredFields()`
- **Tipos de dados:** Validar com `validator.validateDataTypes()`
- **Estrutura:** Validar com `validator.validateResponse()`

## Geração de Dados

- **Biblioteca:** Faker.js
- **Classe:** `DataGenerator`
- **Padrão:** Dados realistas mas únicos para cada teste
- **Limpeza:** Sempre limpar dados criados nos testes

## Retry e Timeout

- **Retry:** 3 tentativas com backoff exponencial
- **Timeout:** 30 segundos por requisição
- **Erros 5xx:** Aplicam retry automático
- **Erros 4xx:** Não aplicam retry

## Logs e Debug

- **Nível:** Configurável (debug, info, warn, error)
- **Formato:** JSON estruturado
- **Contexto:** Inclui userId, requestId, service
- **Rotação:** Máximo 5 arquivos de 10MB cada

## Performance

- **Tempo máximo:** 2 segundos por requisição
- **Memória:** Máximo 100MB
- **CPU:** Máximo 80%
- **Concorrência:** Suporta múltiplas requisições simultâneas

## Considerações para Novos Testes

1. **Sempre usar status 200** para operações de sucesso
2. **Validar schemas** antes de verificar campos específicos
3. **Gerar dados únicos** com DataGenerator
4. **Limpar dados** criados nos testes
5. **Usar retry automático** para operações críticas
6. **Documentar comportamentos** específicos encontrados
7. **Seguir padrão** de nomenclatura dos testes existentes
