# Documentação de Contexto - Migração para Jest + Supertest

## Visão Geral

Este documento serve como contexto completo para a migração do projeto `qa-automation-digisac-api` (Playwright) para `qa-automation-digisac-jest` (Jest + Supertest). O objetivo é manter a mesma funcionalidade, estrutura organizacional e padrões de teste, mas utilizando Jest com Supertest para testes de API.

## 📋 Análise do Projeto Atual

### Estrutura Atual (Playwright)
```
qa-automation-digisac-api/
├── src/
│   ├── api/                    # Testes organizados por endpoint
│   │   ├── auth/              # Autenticação
│   │   ├── connections/       # Conexões (Services)
│   │   ├── organizations/     # Organizações
│   │   ├── messages/          # Mensagens
│   │   ├── campaigns/         # Campanhas
│   │   ├── tags/              # Tags
│   │   ├── notifications/     # Notificações
│   │   └── integrations/      # Integrações
│   ├── utils/                 # Utilitários e helpers
│   │   ├── apiHelper.ts       # Helper para requisições HTTP
│   │   ├── authHelper.ts      # Helper para autenticação
│   │   ├── responseValidator.ts # Validador de respostas
│   │   ├── dataGenerator.ts   # Gerador de dados de teste
│   │   ├── messageDataGenerator.ts # Gerador específico para mensagens
│   │   └── organizationsDataGenerator.ts # Gerador específico para organizações
│   ├── config/                # Configurações
│   │   ├── apiEndpoints.ts    # Endpoints da API Digisac
│   │   └── validationSchemas.ts # Schemas de validação JSON
│   └── types/                 # Definições TypeScript
│       ├── api.types.ts       # Tipos da API
│       ├── common.types.ts    # Tipos comuns
│       ├── organizations.types.ts # Tipos específicos de organizações
│       └── test.types.ts      # Tipos de teste
├── tests/                     # Dados de teste
│   ├── fixtures/              # Fixtures de teste
│   └── schemas/               # Schemas de validação
├── reports/                   # Relatórios de teste
│   ├── allure-results/        # Resultados Allure
│   └── html-report/           # Relatório HTML
├── scripts/                   # Scripts de automação
│   ├── setup.sh              # Script de configuração
│   └── run-tests.sh          # Script de execução
├── docs/                      # Documentação
├── .env.example              # Exemplo de variáveis de ambiente
├── package.json              # Dependências e scripts
├── playwright.config.ts      # Configuração do Playwright
├── tsconfig.json             # Configuração TypeScript
└── README.md                 # Documentação principal
```

### Tecnologias Atuais
- **Framework de Teste**: Playwright
- **Linguagem**: TypeScript
- **Validação**: AJV (JSON Schema)
- **Geração de Dados**: Faker.js
- **Relatórios**: Allure + HTML
- **Autenticação**: OAuth 2.0 (Password Grant)

## 🎯 Objetivos da Migração

### Manter
- ✅ Estrutura organizacional por endpoint/recurso
- ✅ Padrões de nomenclatura e organização
- ✅ Validação de schemas JSON
- ✅ Geração de dados de teste com Faker
- ✅ Sistema de autenticação OAuth 2.0
- ✅ Relatórios detalhados
- ✅ Configuração por ambiente
- ✅ Scripts de automação

### Migrar
- 🔄 Playwright → Jest + Supertest
- 🔄 Configuração de teste
- 🔄 Helpers e utilitários
- 🔄 Estrutura de relatórios
- 🔄 Scripts de execução

## 📚 Documentação da API Digisac

### Base URL
```
https://qa-automacao.digisac.chat/api/v1
```

### Autenticação
- **Tipo**: OAuth 2.0 (Password Grant)
- **Endpoint**: `/api/v1/oauth/token`
- **Headers**: `Content-Type: application/json`
- **Body**:
  ```json
  {
    "grant_type": "password",
    "client_id": "api",
    "client_secret": "secret",
    "username": "<EMAIL>",
    "password": "senha"
  }
  ```
- **Resposta**:
  ```json
  {
    "access_token": "hash_40_caracteres_hexadecimais",
    "token_type": "Bearer",
    "expires_in": 3600,
    "refresh_token": "hash_40_caracteres_hexadecimais"
  }
  ```

### Endpoints Principais

#### 1. Agendamentos (`/api/v1/schedule`)
- `GET /api/v1/schedule` - Listar agendamentos
- `GET /api/v1/schedule/{id}` - Buscar agendamento
- `POST /api/v1/schedule` - Criar agendamento
- `PUT /api/v1/schedule/{id}` - Editar agendamento
- `DELETE /api/v1/schedule/{id}` - Excluir agendamento

#### 2. Tempo Real (`/api/v1/now`)
- `GET /api/v1/now/departments-resume` - Departamentos
- `GET /api/v1/now/attendance-resume` - Atendentes
- `GET /api/v1/now/resume` - Todos os dados

#### 3. Assuntos de Chamado (`/api/v1/ticket-topics`)
- `GET /api/v1/ticket-topics` - Listar assuntos
- `GET /api/v1/ticket-topics/{id}` - Buscar assunto
- `POST /api/v1/ticket-topics` - Criar assunto
- `PUT /api/v1/ticket-topics/{id}` - Editar assunto
- `DELETE /api/v1/ticket-topics/{id}` - Excluir assunto

#### 4. Auditoria (`/api/v1/auth-history`)
- `GET /api/v1/auth-history` - Listar auditorias
- `GET /api/v1/auth-history/{id}` - Buscar auditoria

#### 5. Avaliações (`/api/v1/questions`)
- `GET /api/v1/questions` - Listar avaliações
- `GET /api/v1/questions/{id}` - Buscar avaliação
- `POST /api/v1/questions` - Criar avaliação
- `PUT /api/v1/questions/{id}` - Editar avaliação
- `DELETE /api/v1/questions/{id}` - Excluir avaliação

#### 6. Campanhas (`/api/v1/campaigns`)
- `GET /api/v1/campaigns` - Listar campanhas
- `GET /api/v1/campaigns/{id}` - Buscar campanha
- `GET /api/v1/campaigns/{id}/stats` - Estatísticas
- `POST /api/v1/campaigns` - Criar campanha
- `PUT /api/v1/campaigns/{id}` - Editar campanha
- `DELETE /api/v1/campaigns/{id}` - Excluir campanha
- `POST /api/v1/campaigns/export/csv` - Exportar CSV

#### 7. Campos Personalizados (`/api/v1/custom-fields`)
- `GET /api/v1/custom-fields` - Listar campos
- `GET /api/v1/custom-fields/{id}` - Buscar campo
- `POST /api/v1/custom-fields` - Criar campo
- `PUT /api/v1/custom-fields/{id}` - Editar campo
- `DELETE /api/v1/custom-fields/{id}` - Excluir campo

#### 8. Cargos (`/api/v1/roles`)
- `GET /api/v1/roles` - Listar cargos
- `GET /api/v1/roles/{id}` - Buscar cargo
- `GET /api/v1/roles/{id}?include=permissions` - Permissões
- `POST /api/v1/roles` - Criar cargo
- `PUT /api/v1/roles/{id}` - Editar cargo
- `DELETE /api/v1/roles/{id}` - Excluir cargo

#### 9. Conexões/Services (`/api/v1/services`)
- `GET /api/v1/services` - Listar conexões
- `GET /api/v1/services/{id}` - Buscar conexão
- `POST /api/v1/services` - Criar conexão
- `PUT /api/v1/services/{id}` - Editar conexão
- `POST /api/v1/services/{id}/restart` - Reiniciar
- `POST /api/v1/services/{id}/logout` - Gerar QR Code
- `DELETE /api/v1/services/{id}` - Excluir conexão

#### 10. Contatos (`/api/v1/contacts`)
- `GET /api/v1/contacts` - Listar contatos
- `GET /api/v1/contacts/{id}` - Buscar contato
- `POST /api/v1/contacts` - Criar contato
- `POST /api/v1/contacts/many` - Criar múltiplos
- `PUT /api/v1/contacts/{id}` - Editar contato
- `DELETE /api/v1/contacts/{id}` - Excluir contato
- `POST /api/v1/contacts/export/csv` - Exportar CSV
- `POST /api/v1/contacts/count` - Contar contatos

#### 11. Departamentos (`/api/v1/departments`)
- `GET /api/v1/departments` - Listar departamentos
- `GET /api/v1/departments/{id}` - Buscar departamento
- `GET /api/v1/departments/{id}/users` - Usuários do departamento
- `POST /api/v1/departments` - Criar departamento
- `PUT /api/v1/departments/{id}` - Editar departamento
- `DELETE /api/v1/departments/{id}` - Excluir departamento

#### 12. Entidades (`/api/v1/entities`)
- `GET /api/v1/entities` - Listar entidades
- `GET /api/v1/entities/{id}` - Buscar entidade
- `POST /api/v1/entities` - Criar entidade
- `PUT /api/v1/entities/{id}` - Editar entidade
- `DELETE /api/v1/entities/{id}` - Excluir entidade

#### 13. Feriados (`/api/v1/holidays`)
- `GET /api/v1/holidays` - Listar feriados
- `GET /api/v1/holidays/{id}` - Buscar feriado
- `POST /api/v1/holidays` - Criar feriado
- `PUT /api/v1/holidays/{id}` - Editar feriado
- `DELETE /api/v1/holidays/{id}` - Excluir feriado

#### 14. Filas (`/api/v1/queue`)
- `GET /api/v1/queue` - Listar filas

#### 15. Histórico de Conversas (`/api/v1/chat-history`)
- `POST /api/v1/chat-history/export/csv` - Exportar histórico

#### 16. Métricas (`/api/v1/metrics`)
- `GET /api/v1/metrics/{id}` - Buscar métrica

#### 17. Mensagens (`/api/v1/messages`)
- `GET /api/v1/messages?where[ticketId]={id}` - Listar mensagens
- `POST /api/v1/messages` - Enviar mensagem
- `DELETE /api/v1/messages/{id}` - Excluir mensagem

#### 18. Pessoas (`/api/v1/persons`)
- `GET /api/v1/persons` - Listar pessoas
- `GET /api/v1/persons/{id}` - Buscar pessoa
- `GET /api/v1/persons/{id}?include=contacts` - Contatos da pessoa
- `POST /api/v1/persons` - Criar pessoa
- `PUT /api/v1/persons/{id}` - Editar pessoa
- `DELETE /api/v1/persons/{id}` - Excluir pessoa

#### 19. Respostas Rápidas (`/api/v1/quick-replies`)
- `GET /api/v1/quick-replies` - Listar respostas
- `GET /api/v1/quick-replies/{id}` - Buscar resposta
- `POST /api/v1/quick-replies` - Criar resposta
- `PUT /api/v1/quick-replies/{id}` - Editar resposta
- `DELETE /api/v1/quick-replies/{id}` - Excluir resposta

#### 20. Robôs (`/api/v1/bots`)
- `GET /api/v1/bots` - Listar robôs
- `GET /api/v1/bots/{id}` - Buscar robô
- `GET /api/v1/bots?where[departmentId]={id}` - Por departamento
- `POST /api/v1/bots` - Criar robô
- `PUT /api/v1/bots/{id}` - Editar robô
- `DELETE /api/v1/bots/{id}` - Excluir robô

#### 21. Tags (`/api/v1/tags`)
- `GET /api/v1/tags` - Listar tags
- `GET /api/v1/tags/{id}` - Buscar tag
- `POST /api/v1/tags` - Criar tag
- `PUT /api/v1/tags/{id}` - Editar tag
- `DELETE /api/v1/tags/{id}` - Excluir tag

#### 22. Tickets (`/api/v1/tickets`)
- `GET /api/v1/tickets` - Listar tickets
- `GET /api/v1/tickets/{id}` - Buscar ticket
- `GET /api/v1/tickets/{id}/messages` - Mensagens do ticket
- `POST /api/v1/tickets` - Criar ticket
- `PUT /api/v1/tickets/{id}` - Editar ticket
- `DELETE /api/v1/tickets/{id}` - Excluir ticket
- `POST /api/v1/tickets/{id}/close` - Fechar ticket
- `POST /api/v1/tickets/{id}/trash` - Mover para lixeira
- `POST /api/v1/tickets/{id}/restore` - Restaurar ticket
- `POST /api/v1/tickets/{id}/transfer` - Transferir ticket

#### 23. Tokens (`/api/v1/me/tokens`)
- `GET /api/v1/me/tokens` - Listar tokens
- `DELETE /api/v1/me/tokens/{id}` - Excluir token

#### 24. Usuários (`/api/v1/users`)
- `GET /api/v1/users` - Listar usuários
- `GET /api/v1/users/{id}` - Buscar usuário
- `GET /api/v1/users/{id}?include=roles` - Cargos do usuário
- `POST /api/v1/users` - Criar usuário
- `PUT /api/v1/users/{id}` - Editar usuário
- `DELETE /api/v1/users/{id}` - Excluir usuário

#### 25. Horários de Atendimento (`/api/v1/schedules`)
- `GET /api/v1/schedules` - Listar horários
- `GET /api/v1/schedules/{id}` - Buscar horário
- `POST /api/v1/schedules` - Criar horário
- `PUT /api/v1/schedules/{id}` - Editar horário
- `DELETE /api/v1/schedules/{id}` - Excluir horário

## 🏗️ Padrões de Implementação Atuais

### 1. Estrutura de Classes de API
```typescript
export class ExampleAPI {
  private request: APIRequestContext;
  private baseURL: string;

  constructor(request: APIRequestContext, baseURL: string) {
    this.request = request;
    this.baseURL = baseURL;
  }

  async getAllItems(): Promise<Response> {
    return this.request.get(`${this.baseURL}/api/v1/items`);
  }

  async getItemById(id: string): Promise<Response> {
    return this.request.get(`${this.baseURL}/api/v1/items/${id}`);
  }

  async createItem(data: CreateItemRequest, token: string): Promise<Response> {
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    return this.request.post(`${this.baseURL}/api/v1/items`, {
      headers,
      data
    });
  }
}
```

### 2. Estrutura de Testes
```typescript
test.describe('API - Example', () => {
  let exampleAPI: ExampleAPI;
  let authHelper: AuthHelper;
  let baseURL: string;
  let authToken: string;

  test.beforeAll(async ({ request }) => {
    baseURL = process.env.API_BASE_URL;
    authHelper = new AuthHelper(request, baseURL);
    
    const credentials = authHelper.getCredentialsFromEnv();
    const client = authHelper.getOAuthClientFromEnv();
    const tokenResponse = await authHelper.getOAuthToken(credentials, client);
    authToken = tokenResponse.access_token;
  });

  test.beforeEach(async ({ request }) => {
    exampleAPI = new ExampleAPI(request, baseURL);
  });

  test.describe('GET /items', () => {
    test('deve retornar lista de itens com sucesso', async () => {
      const response = await exampleAPI.getAllItems();
      
      expect(response.status()).toBe(200);
      
      const responseData = await response.json();
      expect(responseData).toBeDefined();
      expect(Array.isArray(responseData.data)).toBe(true);
    });
  });
});
```

### 3. Geradores de Dados
```typescript
export class ExampleDataGenerator {
  static generateValidData(): CreateExampleRequest {
    return {
      name: faker.company.name(),
      email: faker.internet.email(),
      status: 'active' as const
    };
  }

  static generateInvalidData(): Partial<CreateExampleRequest> {
    return {
      // Dados inválidos propositalmente
      name: '',
      email: 'invalid-email'
    };
  }
}
```

### 4. Validação de Schemas
```typescript
export const VALIDATION_SCHEMAS = {
  EXAMPLE: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      status: { type: 'string', enum: ['active', 'inactive'] },
      createdAt: { type: 'string', format: 'date-time' }
    },
    required: ['id', 'name', 'status']
  }
};
```

## 🔧 Configurações Atuais

### package.json
```json
{
  "name": "qa-automation-digisac-api",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "test": "playwright test",
    "test:smoke": "playwright test --project=smoke-tests",
    "test:performance": "playwright test --project=performance-tests",
    "test:api": "playwright test --project=api-tests",
    "allure:generate": "allure generate reports/allure-results --clean -o reports/allure-report",
    "allure:open": "allure open reports/allure-report"
  },
  "devDependencies": {
    "@playwright/test": "^1.40.0",
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0",
    "allure-playwright": "^2.11.0",
    "allure-commandline": "^2.24.0"
  },
  "dependencies": {
    "@faker-js/faker": "^8.0.0",
    "ajv": "^8.0.0",
    "joi": "^17.0.0"
  }
}
```

### playwright.config.ts
```typescript
export default defineConfig({
  testDir: "./src",
  fullyParallel: true,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ["html", { outputFolder: "reports/html-report" }],
    ["allure-playwright", { detail: true, outputFolder: "reports/allure-results" }],
    ["json", { outputFile: "reports/test-results.json" }],
    ["junit", { outputFile: "reports/junit.xml" }]
  ],
  use: {
    baseURL: process.env.API_BASE_URL || "https://qa-automacao.digisac.chat",
    extraHTTPHeaders: { "Accept": "application/json", "Content-Type": "application/json" },
    actionTimeout: 10000,
    navigationTimeout: 10000,
    trace: "retain-on-failure"
  },
  projects: [
    { name: "api-tests", testMatch: /.*\.spec\.ts/ },
    { name: "smoke-tests", testMatch: /.*\.smoke\.spec\.ts/ },
    { name: "performance-tests", testMatch: /.*\.perf\.spec\.ts/ }
  ]
});
```

### tsconfig.json
```json
{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "lib": ["ESNext"],
    "moduleResolution": "Node",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    "resolveJsonModule": true,
    "outDir": "dist",
    "baseUrl": ".",
    "allowSyntheticDefaultImports": true,
    "declaration": true,
    "sourceMap": true,
    "rootDir": "src",
    "paths": {
      "@/*": ["src/*"],
      "@utils/*": ["src/utils/*"],
      "@config/*": ["src/config/*"],
      "@types/*": ["src/types/*"]
    }
  },
  "include": ["src/**/*", "tests/**/*"],
  "exclude": ["node_modules", "dist", "reports"]
}
```

## 📊 Tipos TypeScript Implementados

### Tipos de Autenticação
- `OAuthTokenRequest`
- `OAuthTokenResponse`
- `UserCredentials`
- `OAuthClient`
- `ApiErrorResponse`
- `TokenValidationResult`

### Tipos de Entidades (25+ entidades)
- `Schedule` (Agendamentos)
- `TicketTopic` (Assuntos de chamado)
- `Campaign` (Campanhas)
- `CustomField` (Campos personalizados)
- `Role` (Cargos)
- `Service` (Conexões)
- `Contact` (Contatos)
- `Department` (Departamentos)
- `Entity` (Entidades)
- `Holiday` (Feriados)
- `Message` (Mensagens)
- `Person` (Pessoas)
- `QuickReply` (Respostas rápidas)
- `Bot` (Robôs)
- `Tag` (Tags)
- `Ticket` (Tickets)
- `User` (Usuários)
- E muitos outros...

### Tipos de Validação
- `ValidationResult`
- `ValidationError`
- `ValidationResponse`
- `PerformanceMetrics`
- `LoadTestResult`

## 🧪 Padrões de Teste Implementados

### 1. Testes de Smoke
- Testes básicos de conectividade
- Validação de autenticação
- Verificação de endpoints principais

### 2. Testes de API
- CRUD completo para cada entidade
- Validação de schemas JSON
- Testes de validação de dados
- Testes de autorização
- Testes de paginação
- Testes de filtros

### 3. Testes de Performance
- Tempo de resposta
- Múltiplas requisições simultâneas
- Thresholds de performance

### 4. Testes de Cenários de Borda
- Dados inválidos
- IDs inexistentes
- Caracteres especiais
- Valores extremos

## 🔐 Sistema de Autenticação

### Fluxo OAuth 2.0
1. **Request**: POST `/api/v1/oauth/token`
2. **Headers**: `Content-Type: application/json`
3. **Body**: Credenciais do usuário
4. **Response**: Token de acesso (hash de 40 caracteres hexadecimais)

### Validação de Token
- Formato: 40 caracteres hexadecimais
- Tipo: Bearer
- Expiração: Configurável (padrão 3600s)
- Refresh: Token de renovação disponível

## 📈 Sistema de Relatórios

### Allure
- Relatórios detalhados
- Categorização por features
- Screenshots e traces
- Métricas de performance

### HTML
- Relatório visual
- Filtros por status
- Timeline de execução
- Estatísticas detalhadas

### JUnit
- Compatibilidade com CI/CD
- Integração com ferramentas de build
- Metadados de teste

## 🚀 Scripts de Automação

### setup.sh
- Instalação de dependências
- Configuração de ambiente
- Criação de diretórios
- Validação de requisitos

### run-tests.sh
- Execução por tipo de teste
- Configuração de ambiente
- Geração de relatórios
- Parâmetros customizáveis

## 📝 Documentações Externas Referenciadas

### 1. Documentação Oficial da API Digisac
- **Arquivo**: `digisac-api-doc.md`
- **Conteúdo**: Especificação completa da API
- **Endpoints**: 25+ endpoints documentados
- **Exemplos**: Requests e responses
- **Status**: ✅ Implementado e validado

### 2. Instruções de Implementação
- **Arquivo**: `instrucoes-api.md`
- **Conteúdo**: Guia de implementação do projeto
- **Estrutura**: Diretórios e arquivos
- **Configuração**: Setup e dependências
- **Status**: ✅ Seguido fielmente

### 3. Plano de Ação
- **Arquivo**: `plano-de-acao.md`
- **Conteúdo**: Roadmap de desenvolvimento
- **Fases**: 10 fases de implementação
- **Status**: ✅ 95% concluído
- **Alinhamento**: 100% com API Digisac

## 🎯 Estrutura Proposta para Jest + Supertest

### Nova Estrutura
```
qa-automation-digisac-jest/
├── src/
│   ├── api/                    # Classes de API (mantém estrutura)
│   │   ├── auth/
│   │   ├── connections/
│   │   ├── organizations/
│   │   └── ...
│   ├── utils/                  # Utilitários adaptados
│   │   ├── apiClient.ts        # Cliente HTTP com Supertest
│   │   ├── authHelper.ts       # Helper de autenticação
│   │   ├── responseValidator.ts # Validador de respostas
│   │   └── dataGenerator.ts    # Gerador de dados
│   ├── config/                 # Configurações (mantém)
│   │   ├── apiEndpoints.ts
│   │   └── validationSchemas.ts
│   └── types/                  # Tipos (mantém)
│       ├── api.types.ts
│       └── ...
├── tests/                      # Testes Jest
│   ├── api/                    # Testes por endpoint
│   │   ├── auth.test.ts
│   │   ├── connections.test.ts
│   │   └── ...
│   ├── integration/            # Testes de integração
│   └── fixtures/               # Dados de teste
├── reports/                    # Relatórios
│   ├── junit/                  # JUnit XML
│   ├── coverage/               # Cobertura de código
│   └── html/                   # Relatório HTML
├── scripts/                    # Scripts (adaptados)
├── jest.config.js              # Configuração Jest
├── package.json                # Dependências Jest
└── README.md
```

### Principais Mudanças
1. **Playwright → Jest**: Framework de teste
2. **APIRequestContext → Supertest**: Cliente HTTP
3. **test.describe → describe**: Sintaxe de teste
4. **expect().toBe() → expect().toBe()**: Mantém sintaxe
5. **Relatórios**: Allure → Jest HTML Reporter + Coverage

## 🔄 Mapeamento de Migração

### Playwright → Jest
| Playwright | Jest | Observações |
|------------|------|-------------|
| `test.describe()` | `describe()` | Sintaxe similar |
| `test()` | `it()` | Sintaxe similar |
| `test.beforeAll()` | `beforeAll()` | Mantém funcionalidade |
| `test.beforeEach()` | `beforeEach()` | Mantém funcionalidade |
| `APIRequestContext` | `supertest` | Cliente HTTP diferente |
| `expect().toBe()` | `expect().toBe()` | Mantém sintaxe |
| `response.status()` | `response.status` | Propriedade vs método |
| `response.json()` | `response.body` | Acesso direto ao body |

### Estrutura de Arquivos
| Atual | Novo | Status |
|-------|------|--------|
| `src/api/*/api.ts` | `src/api/*/api.ts` | ✅ Mantém |
| `src/api/*/spec.ts` | `tests/api/*.test.ts` | 🔄 Move e adapta |
| `src/utils/apiHelper.ts` | `src/utils/apiClient.ts` | 🔄 Adapta para Supertest |
| `src/config/` | `src/config/` | ✅ Mantém |
| `src/types/` | `src/types/` | ✅ Mantém |
| `playwright.config.ts` | `jest.config.js` | 🔄 Nova configuração |

## 📋 Checklist de Migração

### Fase 1: Setup Inicial
- [ ] Criar estrutura de diretórios
- [ ] Configurar package.json com Jest + Supertest
- [ ] Configurar jest.config.js
- [ ] Configurar TypeScript
- [ ] Instalar dependências

### Fase 2: Utilitários
- [ ] Migrar AuthHelper para Supertest
- [ ] Criar ApiClient baseado em Supertest
- [ ] Adaptar ResponseValidator
- [ ] Manter DataGenerators
- [ ] Adaptar scripts de automação

### Fase 3: APIs
- [ ] Migrar todas as classes de API
- [ ] Adaptar métodos para Supertest
- [ ] Manter estrutura de endpoints
- [ ] Manter validação de schemas

### Fase 4: Testes
- [ ] Migrar todos os arquivos de teste
- [ ] Adaptar sintaxe Playwright → Jest
- [ ] Manter cenários de teste
- [ ] Manter validações
- [ ] Configurar relatórios

### Fase 5: Validação
- [ ] Executar todos os testes
- [ ] Validar cobertura
- [ ] Verificar relatórios
- [ ] Testar em diferentes ambientes
- [ ] Documentar mudanças

## 🎯 Objetivos de Qualidade

### Manter
- ✅ 100% dos cenários de teste
- ✅ Validação de schemas JSON
- ✅ Geração de dados com Faker
- ✅ Sistema de autenticação OAuth
- ✅ Estrutura organizacional
- ✅ Padrões de nomenclatura

### Melhorar
- 🚀 Performance de execução
- 🚀 Simplicidade de configuração
- 🚀 Relatórios de cobertura
- 🚀 Integração com CI/CD
- 🚀 Debugging e desenvolvimento

## 📚 Referências Técnicas

### Jest
- **Documentação**: https://jestjs.io/docs/getting-started
- **Supertest**: https://github.com/visionmedia/supertest
- **Configuração**: https://jestjs.io/docs/configuration

### Supertest
- **GitHub**: https://github.com/visionmedia/supertest
- **Documentação**: https://github.com/visionmedia/supertest#readme
- **Exemplos**: https://github.com/visionmedia/supertest#example

### TypeScript + Jest
- **Setup**: https://jestjs.io/docs/getting-started#using-typescript
- **Configuração**: https://jestjs.io/docs/configuration#preset-string
- **Tipos**: @types/jest, @types/supertest

## 🏁 Conclusão

Este documento fornece contexto completo para a migração do projeto `qa-automation-digisac-api` (Playwright) para `qa-automation-digisac-jest` (Jest + Supertest). A migração deve manter 100% da funcionalidade atual, adaptando apenas a tecnologia de teste, mas preservando toda a estrutura, padrões e qualidade implementados.

**Status do Projeto Atual**: ✅ 95% completo e 100% alinhado com a API Digisac oficial.

**Próximos Passos**: Implementar a migração seguindo este documento como guia completo.
