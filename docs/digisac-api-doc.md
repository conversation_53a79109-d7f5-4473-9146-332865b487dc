## ☁️ Digisac ☁️

Bem-vindo(a) à Documentação da API Digisac\! Este material foi preparado com muito cuidado pela nossa equipe de Suporte Integração. Nosso objetivo é que ele seja útil para você\!

-----

### **Gerais**

Nesta collection, você vai encontrar uma série de requisições de API para usar no seu ambiente Digisac. Explore à vontade\!

  - **AUTHORIZATION Bearer Token**
  - Token
  - Acionamento flag no robô
  - `{{token}}`
  - `This folder is using Bearer Token from folder Gerais`

-----

### **Agendamentos**

Os agendamentos são utilizados para programar a plataforma para fazer alguma ação em uma determinada data e horário. Com esta ferramenta, você poderá agendar uma abertura de chamado automática com algum cliente, uma notificação a um atendente de sua plataforma ou até fazer as duas ações simultaneamente.

  - **AUTHORIZATION Bearer Token**
  - `This folder is using <PERSON><PERSON>ken from folder Gerais`
  - `GET Listar agendamentos`
  - `{{URL}}/api/v1/schedule?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar agendamento`
  - `{{URL}}/api/v1/schedule/{{scheduleId}}`
  - `PUT Editar agendamento`
  - `{{URL}}/api/v1/schedule/{{scheduleId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "contactId": "{{contactId}}", //ID do contato
  "departmentId": "{{departmentId}}", //ID do departamento
  "files": [],
  "message": "{{text}}",
  "notes": "{{notes}}}}",
  "notificateUser": true, //true or false
  "openTicket": true, //true or false
  "scheduledAt": "{{2023-07-15 10:35:18}}",
  "userId": "{{userId}}"
}
```

  - `POST Criar agendamento`
  - `{{URL}}/api/v1/schedule`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "contactId": "{{contactId}}",
  "departmentId": "{{departmentId}}",
  "files": [{{arquivos}}],
  "message": "{{text}}",
  "notes": "{{note}}}}",
  "notificateUser": true, //true or false
  "openTicket": true, //true or false
  "scheduledAt": "{{2023-07-15 10:35:18}}",
  "userId": "{{userId}}"
}
```

  - `DELETE Excluir agendamento`
  - `{{URL}}/api/v1/schedule/{{scheduleId}}`

-----

### **Agora**

Nesta função é possível visualizar em tempo real todos os atendimentos que estão acontecendo na plataforma por atendente e departamento.

  - **AUTHORIZATION Bearer Token**
  - `This folder is using Bearer Token from folder Gerais`
  - `GET Buscar departamentos (Agora)`
  - `{{URL}}/api/v1/now/departments-resume`
  - `GET Buscar atendentes (Agora)`
  - `{{URL}}/api/v1/now/attendance-resume`
  - `GET Buscar todos os dados (Agora)`
  - `{{URL}}/api/v1/now/resume`

-----

### **Assuntos de chamado**

Assunto de chamado é uma ferramenta utilizada para classificar seus atendimentos. Deverá ser inserido sempre que o usuário fechar o chamado com o contato, desta maneira será necessário definir um "titulo" para aquele atendimento.

  - **AUTHORIZATION Bearer Token**
  - `This folder is using Bearer Token from folder Gerais`
  - `GET Listar assuntos de fechamento`
  - `{{URL}}/api/v1/ticket-topics?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar um assunto específico`
  - `{{URL}}/api/v1/ticket-topics/{{ticketTopicId}}`
  - `PUT Editar assunto específico`
  - `{{URL}}/api/v1/ticket-topics/{{ticketTopicId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "name"
}
```

  - `POST Criar assunto`
  - `{{URL}}/api/v1/ticket-topics`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}"
}
```

  - `DELETE Excluir assunto específico`
  - `{{URL}}/api/v1/ticket-topics/{{ticketTopicId}}`

-----

### **Auditoria de autenticação**

Nesta função é possível filtrar os eventos de criação, alteração e exclusão, por usuários e datas de mudanças no sistema.

  - **AUTHORIZATION Bearer Token**
  - `This folder is using Bearer Token from folder Gerais`
  - `GET Listar auditorias de autenticação`
  - `{{URL}}/api/v1/auth-history?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar auditoria de autenticação`
  - `{{URL}}/api/v1/auth-history/{{authId}}`

-----

### **Autorização**

Gerar o Token de acesso em sua plataforma.

  - **AUTHORIZATION Bearer Token**
  - `This folder is using Bearer Token from folder Gerais`
  - `POST Gerar Token de acesso`
  - `{{URL}}/api/v1/oauth/token`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "grant_type": "password",
  "client_id": "api",
  "client_secret": "secret",
  "username": "{{email}}", //e-mail de login do usuário
  "password": "{{password}}" //senha de login do usuário
}
```

-----

### **Avaliações**

Com a ferramenta de avaliação você poderá medir o desempenho dos serviços prestados aos seus clientes, solicitando sempre, ao final de um atendimento.

  - **AUTHORIZATION Bearer Token**
  - `This folder is using Bearer Token from folder Gerais`
  - `GET Listar avaliações`
  - `{{URL}}/api/v1/questions?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar avaliação`
  - `{{URL}}/api/v1/questions/{{questionId}}`
  - `PUT Editar avaliação`
  - `{{URL}}/api/v1/questions/{{questionId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "duration": "{{duration}}", //duração
  "type": "{{type}}", //tipo (NPS ou CSAT)
  "questionMessage": "{{O que você achou da nossa avaliação?}}",
  "tries": {{tries}}, //tentativas
  "successMessage": "{{Obrigado por responder}}",
  "invalidMessage": "{{Resposta invalida}}"
}
```

  - `POST Criar avaliação`
  - `{{URL}}/api/v1/questions`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "duration": "{{duration}}",
  "type": "{{type}}",
  "questionMessage": "{{O que você achou da nossa avaliação?}}",
  "tries": {{tries}}, //tentativas
  "successMessage": "{{Obrigado por responder}}",
  "invalidMessage": "{{Resposta invalida}}"
}
```

  - `DELETE Excluir avaliação`
  - `{{URL}}/api/v1/questions/{{questionId}}`

-----

### **Campanhas**

Disparo de mensagens em massa.

  - **AUTHORIZATION Bearer Token**
  - `This folder is using Bearer Token from folder Gerais`
  - `GET Listar campanhas`
  - `{{URL}}/api/v1/campaigns?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar campanha`
  - `{{URL}}/api/v1/campaigns/{{campaignsId}}`
  - `GET Extrair status de uma campanha`
  - `{{URL}}/api/v1/campaigns/{{campaignId}}/stats`
  - `GET Visualizar quem criou e enviou uma campanha`
  - `PUT Editar campanha`
  - `DELETE Excluir campanha`
  - `POST Exportar resultados da campanha`
  - `{{URL}}/api/v1/campaigns/export/csv`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "where": {
    "campaignId": "{{campaignId}}" //ID da campanha
  },
  "type": "semiColon"
}
```

  - **Example Request**

<!-- end list -->

```bash
curl --location -g '{{URL}}/api/v1/campaigns/export/csv' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer {{TOKEN}}' \
--data '{
  "where": {
    "campaignId": "{{campaignId}}" //ID da campanha
  },
  "type": "semiColon"
}'
```

-----

### **Campos personalizados**

A ferramenta de Campos Personalizados é utilizada para adicionar campos de preenchimento nos registros de contatos. Com ela é possível criar um pequeno banco de dados para informações mais simples, mas que podem ajudar no dia a dia, como por exemplo, um e-mail do cliente, CPF ou até um número de matrícula.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar campos personalizados`
  - `URL/api/v1/custom-fields?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar campo personalizado`
  - `URL/api/v1/custom-fields/{{customFieldsId}}`
  - `PUT Editar campo personalizado`
  - `URL/api/v1/custom-fields/{{customFieldsId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "type": "text",
  "allowed": "contacts"
}
```

  - `POST Criar campo personalizado`
  - `URL/api/v1/custom-fields`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "type": "text",
  "allowed": "contacts"
}
```

  - `DELETE Excluir campo personalizado`
  - `URL/api/v1/custom-fields/{{customFieldsId}}`

-----

### **Cargos**

Os cargos funcionam como uma hierarquia de acesso dentro da plataforma. Aqui serão configuradas as permissões que o Administrador oferece aos seus usuários. A plataforma já vem configurada para que o Administrador tenha acesso total e o Operador tem um acesso mais limitado, basicamente tem permissão de uso no Chat e visualização de algumas ferramentas.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar cargos`
  - `URL/api/v1/roles?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar cargo`
  - `URL/api/v1/roles/{{roleId}}`
  - `GET Buscar permissões do cargo`
  - `URL/api/v1/roles/{{roleId}}?include=permissions`
  - `PARAMS`
  - `include permissions`
  - `GET Buscar cargos do usuário`
  - `URL/api/v1/users/{{roleId}}?include=roles`
  - `PARAMS`
  - `include roles`
  - `PUT Editar cargo`
  - `URL/api/v1/roles/{{roleId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "displayName": "{{name}}",
  "isAdmin": "{{false}}",
  "permissions": []
}
```

  - `POST Criar cargo`
  - `URL/api/v1/roles`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "displayName": "{{name}}",
  "isAdmin": "{{false}}",
  "permissions": []
}
```

  - `DELETE Excluir cargo`
  - `URL/api/v1/roles/{{roleId}}`

-----

### **Conexões**

Nesta categoria estão reunidas as configurações e informações referentes a todas as conexões que o ambiente Digisac possui.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar conexões`
  - `{{URL}}/api/v1/services?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar conexão`
  - `{{URL}}/api/v1/services/{{serviceId}}`
  - `PUT Editar conexão`
  - `{{URL}}/api/v1/services/{{serviceId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "max_count": "{{número máximo de atendimentos simultâneos}}"
}
```

  - `POST Criar conexão`
  - `{{URL}}/api/v1/services`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "type": "whatsapp",
  "serviceToken": "{{servicoToken}}",
  "webHook": "{{webHook}}",
  "max_count": "{{número máximo de atendimentos simultâneos}}"
}
```

  - `POST Reiniciar conexão`
  - `{{URL}}api/v1/services/{{serviceId}}/restart`
  - `POST Gerar QR Code`
  - `{{URL}}/api/v1/services/{{serviceId}}/logout`
  - `DELETE Excluir conexão`
  - `{{URL}}/api/v1/services/{{serviceId}}`

-----

### **Contatos**

Contatos são os números dos seus clientes que serão salvos dentro da Digisac.

  - **AUTHORIZATION Bearer Token**
  - `This folder is using Bearer Token from folder Gerais`
  - `GET Listar todos os contatos`
  - `{{URL}}/api/v1/contacts?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Listar contatos da conexão`
  - `{{URL}}/api/v1/contacts?where[serviceId]={{serviceId}}?perPage=40`
  - `PARAMS`
  - `where[serviceId]: {{serviceId}}?perPage=40`
  - `GET Buscar contato`
  - `{{URL}}/api/v1/contacts/{{contactId}}`
  - `GET Buscar tags vinculadas a um contato`
  - `{{URL}}/api/v1/contacts/{{contactId}}?include[0]=tags&include[1]`
  - `PARAMS`
  - `include[0]: tags`
  - ` include[1]:  `
  - `GET Buscar contatos vinculados ao Campo Personalizado`
  - `{{URL}}/api/v1/contacts?query={"include":[{"model":"customFieldValues","include":[{"model":"customField","where":{"name":"{{customFieldName}}"}}]}]}`
  - `PARAMS`
  - `query: {"include":[{"model":"customFieldValues","include":[{"model":"customField","where":{"name":"{{customFieldName}}"}}]}]}`
  - `GET Busca de contatos que possuem X campos personalizados preenchidos`
  - `{{URL}}/api/v1/contacts?query={"include":[{"model":"customFieldValues","include":[{"model":"customField","where":{"$or":[{"name":"{{customFieldName1}}"},{"name":"{{customFieldName2}}"},{"name":"{{customFieldName3}}"},{"name":"{{customFieldName4}}"}]}}]}]}`
  - `PARAMS`
  - `query: {"include":[{"model":"customFieldValues","include":[{"model":"customField","where":{"$or":[{"name":"{{customFieldName1}}"},{"name":"{{customFieldName2}}"},{"name":"{{customFieldName3}}"},{"name":"{{customFieldName4}}"}]}}]}]}`
  - `GET Buscar contatos vinculados a Organização`
  - `{{URL}}/api/v1/contacts?query={"include":[{"model":"person","required":"true","where":{"id":"{{organizationId}}"}}],"perPage":"30"}`
  - `PARAMS`
  - `query: {"include":[{"model":"person","required":"true","where":{"id":"{{organizationId}}"}}],"perPage":"30"}`
  - `GET Buscar contatos vinculados a Pessoa`
  - `{{URL}}/api/v1/contacts?query={"include":[{"model":"person","required":"true","where":{"id":"{{personId}}"}}],"perPage":"30"}`
  - `PARAMS`
  - `query: {"include":[{"model":"person","required":"true","where":{"id":"{{personId}}"}}],"perPage":"30"}`
  - `PUT Editar contato`
  - `{{URL}}/api/v1/contacts/{{contactId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "internalName": "{{name}}",
  "number": "{{number}}",
  "serviceId": "{{serviceid}}", //id conexão
  "defaultDepartmentId": null,
  "tagIds": ["{{tagIds}}", "{{tagIds}}"]
}
```

  - `PUT Editar tag do contato`
  - `{{URL}}/api/v1/contacts/{{contactId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "tagIds": ["{{tagIds}}", "{{tagIds}}"] //id tag
}
```

  - `PUT Editar campo personalizado de um contato`
  - `{{URL}}/api/v1/contacts/{{contactId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "customFields": [
    {
      "id": "{{customFieldsId}}",
      "value": "{{texto}}"
    }
  ]
}
```

  - `POST Cadastrar contato`
  - `{{URL}}/api/v1/contacts`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "internalName": "{{name}}",
  "number": "{{number}}", //número contato
  "serviceId": "{{serviceid}}", //id conexão
  "defaultDepartmentId": null,
  "tagIds": ["{{tagIds}}"], //id tag
  "customFields": [ //campos personalizados
    {
      "id": "{{customFieldId}}", //id campo personalizado
      "value": "{{value}}" //texto
    }
  ]
}
```

  - `POST Cadastrar múltiplos contatos`
  - `{{URL}}/api/v1/contacts/many`
  - `Body raw (json)`

<!-- end list -->

```json
[
  {
    "internalName": "{{name}}", //Nome do contato
    "number": "{{number}}", //Número do contato
    "serviceId": "{{serviceid}}", //ID da conexão
    "defaultDepartmentId": null,
    "tagIds": ["{{tagIds}}"] //ID da TAG
  },
  {
    "internalName": "{{name}}",
    "number": "{{number}}",
    "serviceId": "{{serviceid}}",
    "defaultDepartmentId": null,
    "tagIds": ["{{tagIds}}"]
  }
]
```

  - `POST Exportar contatos`
  - `{{URL}}/api/v1/contacts/export/csv`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "where": {
    "visible": true,
    "isGroup": {
      "$eq": false
    },
    "isBroadcast": {
      "$eq": false
    },
    "serviceId": "{{serviceId}}" //id conexão
  },
  "include": [
    "avatar",
    "tags",
    {
      "model": "service",
      "required": true
    },
    "person"
  ],
  "order": [
    [
      "name",
      "ASC"
    ]
  ],
  "page": 1,
  "perPage": 15,
  "type": "comma",
  "serviceType": "whatsapp" //tipo de conexão (e-mail, telegram, etc)
}
```

  - `POST Total de contatos com filtros`
  - `{{URL}}/api/v1/contacts/count`
  - **Observação:** Realizar uma requisição nessa rota retornará a quantidade de chamados do usuário em aberto e a quantidade de chamados na fila dos departamentos em que estiver vinculado.
  - `Body raw (json)`

<!-- end list -->

```json
{
  "query": {
    "isFiltered": false,
    "departmentIds": [
      {{departmentIds}} //id departamento - Opcional
    ]
  }
}
```

  - `DELETE Excluir contatos`
  - `{{URL}}/api/v1/contacts/{{contactId}}`

-----

### **Departamentos**

A ferramenta Departamentos é utilizada para separar seus atendimentos em setores. Você pode, por exemplo, criar departamentos de Vendas, Financeiro e Suporte para organizar sua equipe.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar departamentos`
  - `{{URL}}/api/v1/departments?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar departamento`
  - `{{URL}}/api/v1/departments/{{departmentId}}`
  - `GET Listar usuários por departamento`
  - `{{URL}}/api/v1/departments/{{departmentId}}/users?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `PUT Editar departamento`
  - `{{URL}}/api/v1/departments/{{departmentId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "defaultRoleId": "{{roleId}}",
  "isPrivate": true,
  "tagsIds": ["{{tagIds}}"],
  "departmentsId": ["{{departmentsId}}"]
}
```

  - `POST Criar departamento`
  - `{{URL}}/api/v1/departments`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "defaultRoleId": "{{roleId}}",
  "isPrivate": true,
  "tagsIds": ["{{tagIds}}"],
  "departmentsId": ["{{departmentsId}}"]
}
```

  - `DELETE Excluir departamento`
  - `{{URL}}/api/v1/departments/{{departmentId}}`

-----

### **Entidades**

Entidades são utilizadas para extrair informações do texto em mensagens. É possível criar entidades de e-mail, telefone, CPF, etc.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar entidades`
  - `{{URL}}/api/v1/entities?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar entidade`
  - `{{URL}}/api/v1/entities/{{entityId}}`
  - `PUT Editar entidade`
  - `{{URL}}/api/v1/entities/{{entityId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "description": "{{description}}",
  "values": ["{{value}}"]
}
```

  - `POST Criar entidade`
  - `{{URL}}/api/v1/entities`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "description": "{{description}}",
  "values": ["{{value}}"]
}
```

  - `DELETE Excluir entidade`
  - `{{URL}}/api/v1/entities/{{entityId}}`

-----

### **Feriados**

Esta ferramenta é utilizada para cadastrar dias não úteis, como feriados e fins de semana, para que as regras de atendimento da plataforma sejam aplicadas.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar feriados`
  - `{{URL}}/api/v1/holidays?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar feriado`
  - `{{URL}}/api/v1/holidays/{{holidayId}}`
  - `PUT Editar feriado`
  - `{{URL}}/api/v1/holidays/{{holidayId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "date": "{{date}}"
}
```

  - `POST Criar feriado`
  - `{{URL}}/api/v1/holidays`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "date": "{{date}}"
}
```

  - `DELETE Excluir feriado`
  - `{{URL}}/api/v1/holidays/{{holidayId}}`

-----

### **Filas**

Nesta ferramenta, é possível visualizar o número de contatos que estão aguardando atendimento em um determinado setor.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar filas`
  - `{{URL}}/api/v1/queue?perPage=40`
  - `PARAMS`
  - `perPage: 40`

-----

### **Histórico de conversas**

Essa ferramenta é utilizada para exportar o histórico de conversas entre atendentes e clientes.

  - **AUTHORIZATION Bearer Token**
  - `POST Exportar histórico de conversas`
  - `{{URL}}/api/v1/chat-history/export/csv`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "type": "comma",
  "ticketId": "{{ticketId}}",
  "contactId": "{{contactId}}",
  "start_date": "{{2024-04-09 12:50:49}}",
  "end_date": "{{2024-04-09 12:50:49}}"
}
```

-----

### **Métricas**

Esta ferramenta é utilizada para extrair relatórios com dados de seus atendimentos. Aqui é possível extrair dados como: Tempo médio de espera, Quantidade de atendimentos abertos e fechados, Transferências, entre outros.

  - **AUTHORIZATION Bearer Token**
  - `GET Buscar métricas`
  - `{{URL}}/api/v1/metrics/{{metricId}}`

-----

### **Mensagens**

  - **AUTHORIZATION Bearer Token**
  - `GET Listar mensagens de um chamado`
  - `{{URL}}/api/v1/messages?where[ticketId]={{ticketId}}&perPage=40`
  - `PARAMS`
  - `where[ticketId]: {{ticketId}}`
  - `perPage: 40`
  - `POST Enviar texto`
  - `{{URL}}/api/v1/messages`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "text": "{{text}}", //Texto
  "number": "{{number}}", //Número do contato
  "serviceId": "{{serviceId}}" //ID conexão
}
```

  - `POST Enviar áudio`
  - `{{URL}}/api/v1/messages`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "number": "{{número}}", //Número do contato
  "serviceId": "{{serviceId}}", //ID conexão
  "file": {
    "base64": "{{base64}}", //Converter o áudio em base64
    "mimetype": "audio/mpeg"
  }
}
```

  - `POST Enviar imagem`
  - `{{URL}}/api/v1/messages`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "text": "{{text}}", //Texto
  "number": "{{number}}", //Número do contato
  "serviceId": "{{serviceId}}", //ID conexão
  "file": {
    "base64": "{{base64}}", //Converter a imagem em base64
    "mimetype": "image/jpeg",
    "name": "{{name}}" //Nome imagem
  }
}
```

  - `POST Enviar PDF`
  - `{{URL}}/api/v1/messages`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "text": "{{text}}", //Texto
  "number": "{{number}}", //Número do contato
  "serviceId": "{{serviceId}}", //ID conexão
  "file": {
    "base64": "{{base64}}", //Converter o PDF em base64
    "mimetype": "application/pdf",
    "name": "{{name}}" //Nome arquivo PDF
  }
}
```

  - `POST Enviar comentário`
  - `{{URL}}/api/v1/messages`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "text": "Olá",
  "type": "comment", // chat or comment
  "contactId": "{{contactId}}", //id contato
  "userId": "{{userId}}", //id usuário
  "origin": "bot" // bot or user
}
```

  - `DELETE Excluir mensagem`
  - `{{URL}}/api/v1/messages/{{messageId}}`

-----

### **Pessoas**

Pessoas são os registros que armazenam as informações dos seus clientes, como nome, e-mail, telefone, cargo, empresa, etc.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar pessoas`
  - `{{URL}}/api/v1/persons?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar pessoa`
  - `{{URL}}/api/v1/persons/{{personId}}`
  - `GET Buscar contatos vinculados a pessoa`
  - `{{URL}}/api/v1/persons/{{personId}}?include=contacts`
  - `PARAMS`
  - `include: contacts`
  - `PUT Editar pessoa`
  - `{{URL}}/api/v1/persons/{{personId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "email": "{{email}}",
  "phone": "{{phone}}",
  "jobTitle": "{{jobTitle}}",
  "organizationId": "{{organizationId}}"
}
```

  - `POST Criar pessoa`
  - `{{URL}}/api/v1/persons`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "email": "{{email}}",
  "phone": "{{phone}}",
  "jobTitle": "{{jobTitle}}",
  "organizationId": "{{organizationId}}"
}
```

  - `DELETE Excluir pessoa`
  - `{{URL}}/api/v1/persons/{{personId}}`

-----

### **Redefinir senha**

  - **AUTHORIZATION Bearer Token**
  - `This folder is using Bearer Token from folder Gerais`
  - `POST Solicitar uma nova senha`
  - `{{URL}}/api/v1/reset-password/request`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "email": "{{email}}" //e-mail de acesso do usuário que deseja redefinir a senha
}
```

  - `POST Alterar senha com token`
  - `{{URL}}/api/v1/reset-password/token`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "token": "{{token}}", //token recebido por e-mail
  "password": "{{password}}" //nova senha
}
```

-----

### **Respostas rápidas**

A ferramenta de Respostas Rápidas pode ser utilizada para agilizar o atendimento com o cliente. Com ela, pode-se criar vários textos que normalmente são enviados com frequência aos clientes, como por exemplo: um endereço, uma conta de banco, um formulário ou até alguma mensagem de apresentação dos atendentes.

  - **AUTHORIZATION Bearer Token**
  - `This folder is using Bearer Token from folder Gerais`
  - `GET Listar respostas rápidas`
  - `{{URL}}/api/v1/quick-replies?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Listar textos e departamentos das respostas rápidas`
  - `{{URL}}/api/v1/quick-replies?query={"attributes":["id","text"],"include":[{"model":"departments","attributes":["name","id"],"through":{"attributes":[]}}],"perPage":40}`
  - `PARAMS`
  - `query: {"attributes":["id","text"],"include":[{"model":"departments","attributes":["name","id"],"through":{"attributes":[]}}],"perPage":40}`
  - `PUT Editar resposta rápida`
  - `{{URL}}/api/v1/quick-replies/{{quickRepliesId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "text": "{{text}}",
  "departmentIds": ["{{departmentIds}}"]
}
```

  - `POST Criar resposta rápida`
  - `{{URL}}/api/v1/quick-replies`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "text": "{{text}}",
  "departmentIds": ["{{departmentIds}}"]
}
```

  - `DELETE Excluir resposta rápida`
  - `{{URL}}/api/v1/quick-replies/{{quickRepliesId}}`

-----

### **Robôs**

Os robôs de atendimento, ou mais conhecidos como Chatbot ou somente Bot, realizam o atendimento inicial com os seus clientes. Ele é receptivo, por isso, é necessário que ele receba uma mensagem do contato para ser acionado.

  - **AUTHORIZATION Bearer Token**
  - `This folder is using Bearer Token from folder Gerais`
  - `GET Listar robôs`
  - `{{URL}}/api/v1/bots?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar robô`
  - `{{URL}}/api/v1/bots/{{botId}}`
  - `GET Buscar robôs por departamentos`
  - `{{URL}}/api/v1/bots?where[departmentId]={{departmentId}}`
  - `PARAMS`
  - `where[departmentId]: {{departmentId}}`
  - `PUT Editar robô`
  - `{{URL}}/api/v1/bots/{{botId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "type": "whatsapp",
  "description": "{{description}}",
  "mainDepartmentId": "{{mainDepartmentId}}",
  "departmentsIds": ["{{departmentId}}"],
  "tagsIds": ["{{tagId}}"]
}
```

  - `POST Criar robô`
  - `{{URL}}/api/v1/bots`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "type": "whatsapp",
  "description": "{{description}}",
  "mainDepartmentId": "{{mainDepartmentId}}",
  "departmentsIds": ["{{departmentId}}"],
  "tagsIds": ["{{tagId}}"]
}
```

  - `DELETE Excluir robô`
  - `{{URL}}/api/v1/bots/{{botId}}`

-----

### **Tags**

Tags são etiquetas que ajudam a classificar contatos, chamados, usuários e até departamentos.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar tags`
  - `{{URL}}/api/v1/tags?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar tag`
  - `{{URL}}/api/v1/tags/{{tagId}}`
  - `PUT Editar tag`
  - `{{URL}}/api/v1/tags/{{tagId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "color": "{{#000000}}" //código hexadecimal
}
```

  - `POST Criar tag`
  - `{{URL}}/api/v1/tags`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "color": "{{#000000}}"
}
```

  - `DELETE Excluir tag`
  - `{{URL}}/api/v1/tags/{{tagId}}`

-----

### **Tickets**

Nesta collection estão todas as informações referentes aos chamados que ocorrem na plataforma.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar chamados`
  - `{{URL}}/api/v1/tickets?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar chamado`
  - `{{URL}}/api/v1/tickets/{{ticketId}}`
  - `GET Buscar mensagens de um chamado`
  - `{{URL}}/api/v1/tickets/{{ticketId}}/messages`
  - `GET Buscar tickets por tags`
  - `{{URL}}/api/v1/tickets?query={"include":[{"model":"tags","required":true,"where":{"name":"{{tagName}}"}}]}`
  - `PARAMS`
  - `query: {"include":[{"model":"tags","required":true,"where":{"name":"{{tagName}}"}}]}`
  - `PUT Editar chamado`
  - `{{URL}}/api/v1/tickets/{{ticketId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "subjectId": "{{subjectId}}",
  "tagsIds": ["{{tagIds}}"]
}
```

  - `POST Criar chamado`
  - `{{URL}}/api/v1/tickets`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "contactId": "{{contactId}}",
  "serviceId": "{{serviceId}}",
  "departmentId": "{{departmentId}}",
  "botId": "{{botId}}",
  "isRead": true,
  "inTrash": false,
  "tagsIds": ["{{tagIds}}"]
}
```

  - `DELETE Excluir chamado`
  - `{{URL}}/api/v1/tickets/{{ticketId}}`
  - `POST Fechar chamado`
  - `{{URL}}/api/v1/tickets/{{ticketId}}/close`
  - `POST Mover para lixeira`
  - `{{URL}}/api/v1/tickets/{{ticketId}}/trash`
  - `POST Restaurar chamado`
  - `{{URL}}/api/v1/tickets/{{ticketId}}/restore`
  - `POST Transferir chamado`
  - `{{URL}}/api/v1/tickets/{{ticketId}}/transfer`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "departmentId": "{{departmentId}}", //id do departamento para onde vai o chamado
  "userId": "{{userId}}" //id do usuário para onde vai o chamado
}
```

-----

### **Tokens**

  - **AUTHORIZATION Bearer Token**
  - `GET Listar tokens`
  - `{{URL}}/api/v1/me/tokens?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `DELETE Excluir token`
  - `{{URL}}/api/v1/me/tokens/{{tokenId}}`

-----

### **Usuários**

  - **AUTHORIZATION Bearer Token**
  - `GET Listar usuários`
  - `{{URL}}/api/v1/users?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar usuário`
  - `{{URL}}/api/v1/users/{{userId}}`
  - `PUT Editar usuário`
  - `{{URL}}/api/v1/users/{{userId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "accountId": "{{accountId}}",
  "email": "{{e-mail}}",
  "name": "{{name}}",
  "isAdmin": true //true or false
}
```

  - `POST Criar usuário`
  - `{{URL}}/api/v1/users`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "accountId": "{{accountId}}", //id conta
  "email": {{email}},
  "password": "{{password}}", //senha
  "name": "{{name}}",
  "rolesId": "{{roleId}}", //id cargo
  "organizationIds":[],
  "departmentsId": "{{departmentsId}}" //id departamento
}
```

  - `DELETE Excluir usuário`
  - `{{URL}}/api/v1/users/{{userId}}`

-----

### **Horários de atendimento**

Nesta ferramenta, você pode definir os horários de atendimento da sua plataforma.

  - **AUTHORIZATION Bearer Token**
  - `GET Listar horários de atendimento`
  - `{{URL}}/api/v1/schedules?perPage=40`
  - `PARAMS`
  - `perPage: 40`
  - `GET Buscar horário de atendimento`
  - `{{URL}}/api/v1/schedules/{{scheduleId}}`
  - `PUT Editar horário de atendimento`
  - `{{URL}}/api/v1/schedules/{{scheduleId}}`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "status": "{{status}}",
  "days": ["{{day}}"],
  "startTime": "{{startTime}}",
  "endTime": "{{endTime}}",
  "departmentIds": ["{{departmentId}}"]
}
```

  - `POST Criar horário de atendimento`
  - `{{URL}}/api/v1/schedules`
  - `Body raw (json)`

<!-- end list -->

```json
{
  "name": "{{name}}",
  "status": "{{status}}",
  "days": ["{{day}}"],
  "startTime": "{{startTime}}",
  "endTime": "{{endTime}}",
  "departmentIds": ["{{departmentId}}"]
}
```

  - `DELETE Excluir horário de atendimento`
  - `{{URL}}/api/v1/schedules/{{scheduleId}}`