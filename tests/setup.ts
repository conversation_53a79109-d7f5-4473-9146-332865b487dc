import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

// Configurações globais para os testes
beforeAll(async () => {
  // Configurações iniciais que devem ser executadas antes de todos os testes
  console.log('🚀 Iniciando testes da API Digisac...');
});

afterAll(async () => {
  // Limpeza final após todos os testes
  console.log('✅ Testes finalizados!');
});

// Configurações de timeout global
jest.setTimeout(30000);

// Configurações de console para melhor debugging
const originalConsoleError = console.error;
console.error = (...args: any[]) => {
  if (
    typeof args[0] === 'string' &&
    args[0].includes('Warning: ReactDOM.render is no longer supported')
  ) {
    return;
  }
  originalConsoleError.call(console, ...args);
};
