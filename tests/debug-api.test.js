import { ApiClient } from '../src/utils/apiClient.js';
import { AuthHelper } from '../src/utils/authHelper.js';

describe('Debug API Response', () => {
  it('deve mostrar estrutura real da resposta da API', async () => {
    const baseUrl = 'https://qa-automacao.digisac.chat/api/v1';
    
    // Inicializar cliente API
    const apiClient = new ApiClient(baseUrl);
    const authHelper = new AuthHelper(baseUrl);
    
    // Obter token de autenticação
    const authToken = await authHelper.getAuthenticatedToken();
    apiClient.setAuthToken(authToken);
    
    console.log('🔍 Testando endpoint /services...');
    
    // Fazer requisição para /services
    const response = await apiClient.get('/services');
    
    console.log('📊 Status:', response.status);
    console.log('📋 Headers:', response.headers);
    console.log('📄 Response Body:', JSON.stringify(response.body, null, 2));
    
    // Verificar estrutura da resposta
    if (response.body) {
      console.log('\n🔍 Análise da estrutura:');
      console.log('- Tipo:', typeof response.body);
      console.log('- É array?', Array.isArray(response.body));
      console.log('- Chaves:', Object.keys(response.body));
      
      if (response.body.data) {
        console.log('- data é array?', Array.isArray(response.body.data));
        console.log('- data.length:', response.body.data.length);
        if (response.body.data.length > 0) {
          console.log('- Primeiro item:', JSON.stringify(response.body.data[0], null, 2));
        }
      }
    }
    
    // Sempre passar para não falhar o teste
    expect(true).toBe(true);
  });
});
