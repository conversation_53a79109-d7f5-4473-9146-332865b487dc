/**
 * Testes de Smoke - Conectividade Básica da API Digisac
 * Verifica se a API está acessível e funcionando
 */

import { ApiClient } from '../../src/utils/apiClient';
import { AuthHelper } from '../../src/utils/authHelper';
import { ResponseValidator } from '../../src/utils/responseValidator';

describe('Smoke Tests - Conectividade', () => {
  let apiClient: ApiClient;
  let authHelper: AuthHelper;
  let validator: ResponseValidator;
  let authToken: string;

  beforeAll(async () => {
    const baseUrl = process.env.API_BASE_URL || 'https://qa-automacao.digisac.chat/api/v1';
    
    // Inicializar cliente API
    apiClient = new ApiClient(baseUrl);
    authHelper = new AuthHelper(baseUrl);
    validator = new ResponseValidator();
  });

  afterAll(async () => {
    // Limpeza final
    if (authHelper) {
      await authHelper.logout();
    }
  });

  // describe('Conectividade Básica', () => {
  //   it('deve estar acessível e respondendo', async () => {
  //     // Teste básico de conectividade
  //     const response = await apiClient.get('/oauth/token');
      
  //     // Esperamos um erro 400, 401 ou 405, mas não erro de conectividade
  //     expect([400, 401, 405]).toContain(response.status);
  //   });

  //   it('deve ter headers corretos', async () => {
  //     const response = await apiClient.get('/oauth/token');
      
  //     // Verificar headers básicos
  //     expect(response.headers['content-type']).toContain('application/json');
  //   });

  //   it('deve responder em tempo razoável', async () => {
  //     const startTime = Date.now();
      
  //     const response = await apiClient.get('/oauth/token');
      
  //     const endTime = Date.now();
  //     const responseTime = endTime - startTime;
      
  //     // Deve responder em menos de 5 segundos
  //     expect(responseTime).toBeLessThan(5000);
  //   });
  // });

  describe('Autenticação', () => {
    it('deve obter token de autenticação com credenciais válidas', async () => {
      try {
        const token = await authHelper.getAuthenticatedToken();
        
        expect(token).toBeDefined();
        expect(typeof token).toBe('string');
        expect(token.length).toBe(40);
        
        const hexPattern = /^[a-fA-F0-9]{40}$/;
        expect(hexPattern.test(token)).toBe(true);
        
        authToken = token;
      } catch (error) {
        expect(error instanceof Error ? error.message : String(error)).toContain('Falha na autenticação');
      }
    });

    it('deve configurar autenticação no cliente API', async () => {
      if (!authToken) {
        console.warn('Token de autenticação não disponível, pulando teste');
        return;
      }

      apiClient.setAuthToken(authToken);
      
      // Verificar se o token foi configurado
      const requestInfo = apiClient.getRequestInfo();
      expect(requestInfo.headers['Authorization']).toBe(`Bearer ${authToken}`);
    });

    it('deve validar token de autenticação', async () => {
      if (!authToken) {
        console.warn('Token de autenticação não disponível, pulando teste');
        return;
      }

      const isValid = await authHelper.validateToken(authToken);
      expect(isValid).toBe(true);
    });
  });

  describe('Endpoints Principais', () => {
    beforeAll(async () => {
      if (!authToken) {
        try {
          authToken = await authHelper.getAuthenticatedToken();
          apiClient.setAuthToken(authToken);
        } catch (error) {
          console.warn('Não foi possível obter token de autenticação');
        }
      }
    });

    it('deve acessar endpoint de serviços', async () => {
      if (!authToken) {
        console.warn('Token de autenticação não disponível, pulando teste');
        return;
      }

      const response = await apiClient.get('/api/v1/services?perPage=1');
      
      // Deve retornar 200, 401 ou 404 (dependendo da autenticação)
      expect([200, 401, 404]).toContain(response.status);
    });

    it('deve acessar endpoint de contatos', async () => {
      if (!authToken) {
        console.warn('Token de autenticação não disponível, pulando teste');
        return;
      }

      const response = await apiClient.get('/api/v1/contacts?perPage=1');
      
      // Deve retornar 200, 401 ou 404 (dependendo da autenticação)
      expect([200, 401, 404]).toContain(response.status);
    });

    it('deve acessar endpoint de departamentos', async () => {
      if (!authToken) {
        console.warn('Token de autenticação não disponível, pulando teste');
        return;
      }

      const response = await apiClient.get('/api/v1/departments?perPage=1');
      
      // Deve retornar 200, 401 ou 404 (dependendo da autenticação)
      expect([200, 401, 404]).toContain(response.status);
    });

    it('deve acessar endpoint de usuários', async () => {
      if (!authToken) {
        console.warn('Token de autenticação não disponível, pulando teste');
        return;
      }

      const response = await apiClient.get('/api/v1/users?perPage=1');
      
      // Deve retornar 200, 401 ou 404 (dependendo da autenticação)
      expect([200, 401, 404]).toContain(response.status);
    });

    it('deve acessar endpoint de tags', async () => {
      if (!authToken) {
        console.warn('Token de autenticação não disponível, pulando teste');
        return;
      }

      const response = await apiClient.get('/api/v1/tags?perPage=1');
      
      // Deve retornar 200, 401 ou 404 (dependendo da autenticação)
      expect([200, 401, 404]).toContain(response.status);
    });
  });

  // describe('Validação de Resposta', () => {
  //   it('deve retornar JSON válido', async () => {
  //     const response = await apiClient.get('/oauth/token');
      
  //     // Verificar se a resposta é JSON válido
  //     expect(() => JSON.parse(JSON.stringify(response.body))).not.toThrow();
  //   });

  //   it('deve ter estrutura de erro válida quando apropriado', async () => {
  //     const response = await apiClient.get('/oauth/token');
      
  //     if (response.status >= 400) {
  //       const errorValidation = validator.validateErrorResponse(response);
  //       expect(errorValidation.isValid).toBe(true);
  //     }
  //   });
  // });

  describe('Configuração de Ambiente', () => {
    it('deve ter variáveis de ambiente configuradas', () => {
      expect(process.env.API_BASE_URL).toBeDefined();
      expect(process.env.API_USERNAME).toBeDefined();
      expect(process.env.API_PASSWORD).toBeDefined();
    });

    it('deve ter URL base válida', () => {
      const baseUrl = process.env.API_BASE_URL;
      
      expect(baseUrl).toMatch(/^https?:\/\/.+/);
      expect(baseUrl).toContain('digisac.chat');
    });

    it('deve ter credenciais válidas', () => {
      const username = process.env.API_USERNAME;
      const password = process.env.API_PASSWORD;
      
      expect(username).toBeDefined();
      expect(password).toBeDefined();
      expect(username).toContain('@'); // Deve ser um email
      expect(password?.length).toBeGreaterThan(0);
    });
  });

  describe('Performance Básica', () => {
    it('deve responder rapidamente para requisições simples', async () => {
      const startTime = Date.now();
      
      const response = await apiClient.get('/oauth/token');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // Deve responder em menos de 3 segundos
      expect(responseTime).toBeLessThan(3000);
    });

    it('deve suportar requisições concorrentes', async () => {
      const promises = Array.from({ length: 5 }, () => 
        apiClient.get('/oauth/token')
      );
      
      const responses = await Promise.all(promises);
      
      // Todas as requisições devem ter respondido
      expect(responses).toHaveLength(5);
      
      // Todas devem ter status válido
      responses.forEach(response => {
        expect([200, 400, 401, 404, 405]).toContain(response.status);
      });
    });
  });

  describe('Logs e Debugging', () => {
    it('deve registrar informações de debug quando habilitado', () => {
      const debugMode = process.env.DEBUG_MODE === 'true';
      
      if (debugMode) {
        console.log('Modo debug habilitado');
        console.log('URL base:', process.env.API_BASE_URL);
        console.log('Usuário:', process.env.API_USERNAME);
      }
      
      // Teste sempre passa, mas registra informações quando debug está habilitado
      expect(true).toBe(true);
    });
  });
});
