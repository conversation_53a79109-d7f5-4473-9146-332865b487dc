import { ApiClient } from '../../src/utils/apiClient';
import { AuthHelper } from '../../src/utils/authHelper';
import { ResponseValidator } from '../../src/utils/responseValidator';
import { DataGenerator } from '../../src/utils/dataGenerator';
import { DepartmentsAPI } from '../../src/api/departments/departments.api';
import { VALIDATION_SCHEMAS } from '../../src/config/validationSchemas';
import { API_ENDPOINTS } from '../../src/config/apiEndpoints';

describe('API - Departments (Departamentos)', () => {
  let apiClient: ApiClient;
  let authHelper: AuthHelper;
  let validator: ResponseValidator;
  let departmentsAPI: DepartmentsAPI;
  let authToken: string;
  let beforeEachResponse: any;
  let shouldCleanUp = true;
  const accountId = process.env.API_ACCOUNT_ID;

  beforeAll(async () => {
    const baseUrl = process.env.API_BASE_URL || 'https://qa-automacao.digisac.chat/api/v1';

    apiClient = new ApiClient(baseUrl);
    authHelper = new AuthHelper(baseUrl);
    validator = new ResponseValidator();

    authToken = await authHelper.getAuthenticatedToken();
    apiClient.setAuthToken(authToken);
    
    DataGenerator.setAuthToken(authToken);

    departmentsAPI = new DepartmentsAPI(apiClient);
    shouldCleanUp = true;
  });

  afterAll(async () => {
    await authHelper.logout();
  });
  describe('Departamentos - CRUD Completo', () => {
    
    beforeEach(async () => {
      const name = DataGenerator.generateDepartmentData().name;
      const response = await DataGenerator.createDepartment(name);
      validator.validateStatus(response, 200);   
      const validationResult = validator.validateResponse(response, VALIDATION_SCHEMAS.DEPARTMENT);
      expect(validationResult.isValid).toBe(true);
      expect(response.body).toEqual({
        name: name,
        id: expect.any(String),
        accountId: accountId,
        archivedAt: null,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
        distributionId: null
      });
      beforeEachResponse = response.body;
    });

    afterEach(async () => {
      if (shouldCleanUp) {
        await DataGenerator.deleteDepartment(beforeEachResponse.id);
        const deletedResponse = await apiClient.get(API_ENDPOINTS.DEPARTMENTS.BY_ID(beforeEachResponse.id));
        validator.validateStatus(deletedResponse, 200);
        expect(deletedResponse.body).toBeNull();
      } else {
        console.log('🔄 Skipping cleanup for department:', beforeEachResponse.id);
      }
    });

    it('1. POST /departments - deve criar um novo departamento', async () => {
      const createdResponse = await apiClient.get(API_ENDPOINTS.DEPARTMENTS.BY_ID(beforeEachResponse.id));
      validator.validateStatus(createdResponse, 200);
      console.log('⬇️ Response body createdResponse:', createdResponse.body);
      expect(createdResponse.body).toEqual({
        id: beforeEachResponse.id,
        name: beforeEachResponse.name,
        accountId: accountId,
        createdAt: beforeEachResponse.createdAt,
        updatedAt: beforeEachResponse.updatedAt,
        archivedAt: null,
        distributionId: null,
        usersCount: "0"
      });
    });

    it('2. PUT /departments/{id} - deve editar um departamento', async () => {
      const updatePayload = { name: 'Dept QA - Atualizado' };
      const response = await apiClient.put(API_ENDPOINTS.DEPARTMENTS.BY_ID(beforeEachResponse.id), updatePayload);
      validator.validateStatus(response, 200);
      const validationResult = validator.validateResponse(response, VALIDATION_SCHEMAS.DEPARTMENT);
      expect(validationResult.isValid).toBe(true);
      expect(response.body).toEqual({
        id: beforeEachResponse.id,
        name: updatePayload.name,
        accountId: accountId,
        createdAt: beforeEachResponse.createdAt,
        updatedAt: expect.any(String),
        archivedAt: null,
        distributionId: null,
      });
      expect(new Date(response.body.updatedAt) > new Date(response.body.createdAt)).toBe(true);

      const updatedResponse = await apiClient.get(API_ENDPOINTS.DEPARTMENTS.BY_ID(beforeEachResponse.id));
      validator.validateStatus(updatedResponse, 200);
      expect(updatedResponse.body).toEqual({
        id: beforeEachResponse.id,
        name: updatePayload.name,
        accountId: accountId,
        createdAt: beforeEachResponse.createdAt,
        updatedAt: expect.any(String),
        archivedAt: null,
        distributionId: null,
        usersCount: "0"
      });
      expect(new Date(updatedResponse.body.updatedAt) > new Date(updatedResponse.body.createdAt)).toBe(true);
    });

    it('3. GET /departments - deve listar todos os departamentos', async () => {
      const response = await apiClient.get(API_ENDPOINTS.DEPARTMENTS.LIST);
      validator.validateStatus(response, 200);
      
      const validationResult = validator.validateResponse(response, VALIDATION_SCHEMAS.PAGINATED_RESPONSE);
      expect(validationResult.isValid).toBe(true);
      
      const requiredFields = ['data', 'total', 'limit', 'skip', 'currentPage', 'lastPage', 'from', 'to'];
      const fieldsValidation = validator.validateRequiredFields(response, requiredFields);
      expect(fieldsValidation.isValid).toBe(true);
      
      const departmentExists = response.body.data.some((dept: any) => dept.id === beforeEachResponse.id);
      expect(departmentExists).toBe(true);
    });

    it('4. GET /departments - deve paginar corretamente', async () => {
      const response = await apiClient.getWithQuery(API_ENDPOINTS.DEPARTMENTS.BASE, { perPage: 10, page: 1 });
      
      validator.validateStatus(response, 200);
      expect(response.body.limit).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThanOrEqual(1);
    });

    it('5. GET /departments/{id} - deve buscar departamento por ID', async () => {
      const response = await apiClient.get(API_ENDPOINTS.DEPARTMENTS.BY_ID(beforeEachResponse.id));
      validator.validateStatus(response, 200);
      const validationResult = validator.validateResponse(response, VALIDATION_SCHEMAS.DEPARTMENT);
      expect(validationResult.isValid).toBe(true);
      expect(response.body).toEqual({
        id: beforeEachResponse.id,
        name: beforeEachResponse.name,
        accountId: accountId,
        createdAt: beforeEachResponse.createdAt,
        updatedAt: beforeEachResponse.updatedAt,
        archivedAt: null,
        distributionId: null,
        usersCount: "0"
      });
    });

    it('6. POST /departments/{id}/archive - deve arquivar um departamento', async () => {
      const initialResponse = await apiClient.get(API_ENDPOINTS.DEPARTMENTS.BY_ID(beforeEachResponse.id));
      validator.validateStatus(initialResponse, 200);
      const validationInitiaResult = validator.validateResponse(initialResponse, VALIDATION_SCHEMAS.DEPARTMENT);
      expect(validationInitiaResult.isValid).toBe(true);
      expect(initialResponse.body).toEqual({
        id: beforeEachResponse.id,
        name: beforeEachResponse.name,
        accountId: accountId,
        createdAt: beforeEachResponse.createdAt,
        updatedAt: beforeEachResponse.updatedAt,
        archivedAt: null,
        distributionId: null,
        usersCount: "0"
      });
      
      const archiveResponse = await apiClient.post(`${API_ENDPOINTS.DEPARTMENTS.BY_ID(beforeEachResponse.id)}/archive`, {"archive": 'true'});
      validator.validateStatus(archiveResponse, 200);
      const validationArchiveResult = validator.validateResponse(archiveResponse, VALIDATION_SCHEMAS.DEPARTMENT);
      expect(validationArchiveResult.isValid).toBe(true);
      expect(archiveResponse.body).toEqual({
        id: beforeEachResponse.id,
        name: beforeEachResponse.name,
        accountId: accountId,
        createdAt: beforeEachResponse.createdAt,
        updatedAt: expect.any(String),
        archivedAt: expect.any(String),
        distributionId: null,
      });
      expect(new Date(archiveResponse.body.archivedAt) > new Date(archiveResponse.body.createdAt)).toBe(true);
      expect(new Date(archiveResponse.body.updatedAt) > new Date(archiveResponse.body.createdAt)).toBe(true);
      
      const archivedResponse = await apiClient.get(API_ENDPOINTS.DEPARTMENTS.BY_ID(beforeEachResponse.id));
      validator.validateStatus(archivedResponse, 200);
      const validationArchivedResult = validator.validateResponse(archivedResponse, VALIDATION_SCHEMAS.DEPARTMENT);
      expect(validationArchivedResult.isValid).toBe(true);
      expect(archivedResponse.body).toEqual({
        id: beforeEachResponse.id,
        name: beforeEachResponse.name,
        accountId: accountId,
        createdAt: beforeEachResponse.createdAt,
        updatedAt: expect.any(String),
        archivedAt: expect.any(String),
        distributionId: null,
        usersCount: "0"
      });
      expect(new Date(archivedResponse.body.archivedAt) > new Date(archivedResponse.body.createdAt)).toBe(true);
      expect(new Date(archivedResponse.body.updatedAt) > new Date(archivedResponse.body.createdAt)).toBe(true);
    });

    it('7. DELETE /departments/{id} - deve excluir um departamento', async () => {
      shouldCleanUp = false;
      
      const response = await DataGenerator.deleteDepartment(beforeEachResponse.id);
      validator.validateStatus(response, 200);
      
      const deletedResponse = await apiClient.get(API_ENDPOINTS.DEPARTMENTS.BY_ID(beforeEachResponse.id));
      validator.validateStatus(deletedResponse, 200);
      expect(deletedResponse.body).toBeNull();
    });

    it('8. POST /departments - deve retornar erro 409 para nome já existente ao criar', async () => {
      const response = await apiClient.post(API_ENDPOINTS.DEPARTMENTS.BASE, {"name":beforeEachResponse.name});
      validator.validateStatus(response, 409);
      expect(response.body).toEqual({
        error: "HttpError",
        message: "There already exists a resource with same fields name,accountId.",
        status: 409,
        extra: {}
      });
    });
  });

  describe('Departamentos - Campos obrigatórios', () => {
    it('1. POST /departments - deve retornar erro 400 para campos obrigatórios - Nome vazio ao criar', async () => {
      const response = await apiClient.post(API_ENDPOINTS.DEPARTMENTS.BASE, {"name": ""});
      validator.validateStatus(response, 400);
      expect(response.body.errors.body.name.messages).toEqual(["Required."]);
    });

    it('2. POST /departments - deve retornar erro 400 para campos obrigatórios - Nome apenas com espaço ao criar', async () => {
      const response = await apiClient.post(API_ENDPOINTS.DEPARTMENTS.BASE, {"name": " "});
      validator.validateStatus(response, 400);
      expect(response.body.errors.body.name.messages).toEqual(["Required."]);
    });

    it('3. POST /departments - deve retornar erro 400 para campos obrigatórios - Body vazio ao criar', async () => {
      const response = await apiClient.post(API_ENDPOINTS.DEPARTMENTS.BASE, {});
      validator.validateStatus(response, 400);
      expect(response.body.errors.body.name.messages).toEqual(["Required."]);
    });

    it('4. PUT /departments/{id} - deve retornar erro 400 para campos obrigatórios - Nome vazio ao atualizar', async () => {
      const name = DataGenerator.generateDepartmentData().name;
      const postResponse = await DataGenerator.createDepartment(name);
      validator.validateStatus(postResponse, 200);
      const validationResult = validator.validateResponse(postResponse, VALIDATION_SCHEMAS.DEPARTMENT);
      expect(validationResult.isValid).toBe(true);
      expect(postResponse.body).toEqual({
        id: expect.any(String),
        name: name,
        accountId: accountId,
        archivedAt: null,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
        distributionId: null
      });

      try {
      const response = await apiClient.put(API_ENDPOINTS.DEPARTMENTS.BY_ID(postResponse.body.id), {"name": ""});
        validator.validateStatus(response, 400);
        expect(response.body.errors.body.name.messages).toEqual(["Required."]);
      } finally {
        await DataGenerator.deleteDepartment(postResponse.body.id);
      }
    });

    it('5. PUT /departments/{id} - deve retornar erro 400 para campos obrigatórios - Nome apenas com espaço ao atualizar', async () => {
      const name = DataGenerator.generateDepartmentData().name;
      const postResponse = await DataGenerator.createDepartment(name);
      validator.validateStatus(postResponse, 200);
      const validationResult = validator.validateResponse(postResponse, VALIDATION_SCHEMAS.DEPARTMENT);
      expect(validationResult.isValid).toBe(true);
      expect(postResponse.body).toEqual({
        id: expect.any(String),
        name: name,
        accountId: accountId,
        archivedAt: null,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
        distributionId: null
      });

      try {
      const response = await apiClient.put(API_ENDPOINTS.DEPARTMENTS.BY_ID(postResponse.body.id), {"name": " "});
        validator.validateStatus(response, 400);
        expect(response.body.errors.body.name.messages).toEqual(["Required."]);
      } finally {
        await DataGenerator.deleteDepartment(postResponse.body.id);
      }
    });

    it('6. PUT /departments/{id} - Não deve atualizar - Body vazio ao atualizar', async () => {
      const name = DataGenerator.generateDepartmentData().name;
      const postResponse = await DataGenerator.createDepartment(name);
      validator.validateStatus(postResponse, 200);
      const validationResult = validator.validateResponse(postResponse, VALIDATION_SCHEMAS.DEPARTMENT);
      expect(validationResult.isValid).toBe(true);
      expect(postResponse.body).toEqual({
        id: expect.any(String),
        name: name,
        accountId: accountId,
        archivedAt: null,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
        distributionId: null
      });

      try {
      const response = await apiClient.put(API_ENDPOINTS.DEPARTMENTS.BY_ID(postResponse.body.id), {});
        validator.validateStatus(response, 200);
        const validationResult = validator.validateResponse(response, VALIDATION_SCHEMAS.DEPARTMENT);
        expect(validationResult.isValid).toBe(true);
        expect(response.body).toEqual({
          id: postResponse.body.id,
          name: postResponse.body.name,
          accountId: accountId,
          archivedAt: postResponse.body.archivedAt,
          createdAt: postResponse.body.createdAt,
          updatedAt: postResponse.body.updatedAt,
          distributionId: postResponse.body.distributionId
        });
      } finally {
        await DataGenerator.deleteDepartment(postResponse.body.id);
      }
    });
  });
});


